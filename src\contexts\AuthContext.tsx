
import React, { createContext, useContext, useState, useEffect } from "react";
import { toast } from "sonner";
import AuthService, { User } from "@/services/auth.service";
import { useLogin as useLoginHook, useReg<PERSON> as useRegisterHook, useLogout as useLogoutHook } from "@/hooks/use-auth";

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}



const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Use the React Query hooks
  const loginMutation = useLoginHook();
  const registerMutation = useRegisterHook();
  const logoutFn = useLogoutHook();

  useEffect(() => {
    // Check for saved auth in localStorage
    const savedUser = localStorage.getItem("nutrisnap_user");
    if (savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser);
        setUser(parsedUser);
      } catch (error) {
        console.error("Error parsing saved user:", error);
        localStorage.removeItem("nutrisnap_user");
        localStorage.removeItem("nutrisnap_token");
      }
    }
    setLoading(false);
  }, []);

  // Update user state when login mutation succeeds
  useEffect(() => {
    if (loginMutation.data?.user) {
      setUser(loginMutation.data.user);
    }
  }, [loginMutation.data]);

  // Update user state when register mutation succeeds
  useEffect(() => {
    if (registerMutation.data?.user) {
      setUser(registerMutation.data.user);
    }
  }, [registerMutation.data]);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      await loginMutation.mutateAsync({ email, password });
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signup = async (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) => {
    setLoading(true);
    try {
      await registerMutation.mutateAsync(userData);
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    logoutFn();
    setUser(null);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        signup,
        logout,
        isAuthenticated: !!user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
