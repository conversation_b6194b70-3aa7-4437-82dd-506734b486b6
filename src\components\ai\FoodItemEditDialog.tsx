import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Trash2, Plus } from "lucide-react";
import { ProcessedFoodItem } from "@/hooks/use-ai-analysis";
import { toast } from "sonner";

interface FoodItemEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  foodItem: ProcessedFoodItem | null;
  onSave: (updatedItem: ProcessedFoodItem) => void;
  onDelete?: () => void;
}

const commonPortionSizes = [
  "1 small",
  "1 medium", 
  "1 large",
  "1 cup",
  "1/2 cup",
  "1/4 cup",
  "1 tablespoon",
  "1 teaspoon",
  "1 slice",
  "1 piece",
  "1 serving",
  "100g",
  "50g",
  "200g"
];

const FoodItemEditDialog: React.FC<FoodItemEditDialogProps> = ({
  isOpen,
  onClose,
  foodItem,
  onSave,
  onDelete,
}) => {
  const [editedItem, setEditedItem] = useState<ProcessedFoodItem | null>(null);
  const [customPortionSize, setCustomPortionSize] = useState("");
  const [showCustomPortion, setShowCustomPortion] = useState(false);

  // Initialize form when dialog opens or foodItem changes
  useEffect(() => {
    if (foodItem) {
      setEditedItem({ ...foodItem });
      setCustomPortionSize("");
      setShowCustomPortion(!commonPortionSizes.includes(foodItem.portionSize));
    }
  }, [foodItem, isOpen]);

  const handleSave = () => {
    if (!editedItem) return;

    // Validation
    if (!editedItem.name.trim()) {
      toast.error("Food name is required");
      return;
    }

    if (!editedItem.portionSize.trim()) {
      toast.error("Portion size is required");
      return;
    }

    if (editedItem.calories < 0 || editedItem.protein < 0 || editedItem.carbohydrates < 0 || editedItem.fats < 0) {
      toast.error("Nutritional values cannot be negative");
      return;
    }

    onSave(editedItem);
    onClose();
    toast.success(`Updated ${editedItem.name}`);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
      onClose();
      toast.success(`Removed ${editedItem?.name}`);
    }
  };

  const handlePortionSizeChange = (value: string) => {
    if (value === "custom") {
      setShowCustomPortion(true);
      setCustomPortionSize("");
    } else {
      setShowCustomPortion(false);
      setEditedItem(prev => prev ? { ...prev, portionSize: value } : null);
    }
  };

  const handleCustomPortionSizeChange = (value: string) => {
    setCustomPortionSize(value);
    setEditedItem(prev => prev ? { ...prev, portionSize: value } : null);
  };

  if (!editedItem) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Food Item</DialogTitle>
          <DialogDescription>
            Modify the detected food item details and nutritional information.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Food Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Food Name</Label>
            <Input
              id="name"
              value={editedItem.name}
              onChange={(e) => setEditedItem({ ...editedItem, name: e.target.value })}
              placeholder="Enter food name"
            />
          </div>

          {/* Portion Size */}
          <div className="space-y-2">
            <Label htmlFor="portion">Portion Size</Label>
            {!showCustomPortion ? (
              <Select
                value={editedItem.portionSize}
                onValueChange={handlePortionSizeChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select portion size" />
                </SelectTrigger>
                <SelectContent>
                  {commonPortionSizes.map((size) => (
                    <SelectItem key={size} value={size}>
                      {size}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">
                    <div className="flex items-center">
                      <Plus className="h-4 w-4 mr-2" />
                      Custom portion size
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <div className="space-y-2">
                <Input
                  value={customPortionSize}
                  onChange={(e) => handleCustomPortionSizeChange(e.target.value)}
                  placeholder="Enter custom portion size (e.g., 150g, 2 pieces)"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShowCustomPortion(false);
                    setEditedItem({ ...editedItem, portionSize: commonPortionSizes[0] });
                  }}
                >
                  Use standard portions
                </Button>
              </div>
            )}
          </div>

          {/* Nutritional Information */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Nutritional Information</Label>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="calories">Calories</Label>
                <Input
                  id="calories"
                  type="number"
                  min="0"
                  value={editedItem.calories}
                  onChange={(e) => setEditedItem({ ...editedItem, calories: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="protein">Protein (g)</Label>
                <Input
                  id="protein"
                  type="number"
                  min="0"
                  step="0.1"
                  value={editedItem.protein}
                  onChange={(e) => setEditedItem({ ...editedItem, protein: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="carbs">Carbohydrates (g)</Label>
                <Input
                  id="carbs"
                  type="number"
                  min="0"
                  step="0.1"
                  value={editedItem.carbohydrates}
                  onChange={(e) => setEditedItem({ ...editedItem, carbohydrates: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fats">Fats (g)</Label>
                <Input
                  id="fats"
                  type="number"
                  min="0"
                  step="0.1"
                  value={editedItem.fats}
                  onChange={(e) => setEditedItem({ ...editedItem, fats: Number(e.target.value) })}
                />
              </div>
            </div>
          </div>

          {/* Confidence Level */}
          <div className="space-y-2">
            <Label htmlFor="confidence">AI Confidence Level (%)</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="confidence"
                type="number"
                min="0"
                max="100"
                value={editedItem.confidence}
                onChange={(e) => setEditedItem({ ...editedItem, confidence: Number(e.target.value) })}
              />
              <Badge variant="outline">
                {editedItem.confidence >= 90 ? "High" : editedItem.confidence >= 70 ? "Medium" : "Low"}
              </Badge>
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={editedItem.notes || ""}
              onChange={(e) => setEditedItem({ ...editedItem, notes: e.target.value })}
              placeholder="Add any additional notes about this food item..."
              rows={3}
            />
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <div>
            {onDelete && (
              <Button
                variant="destructive"
                onClick={handleDelete}
                className="flex items-center"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Remove Item
              </Button>
            )}
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FoodItemEditDialog;
