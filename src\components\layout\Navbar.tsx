
import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { Camera, LogIn, Menu, X } from "lucide-react";

const Navbar = () => {
  const { isAuthenticated, login, signup, logout } = useAuth();
  const navigate = useNavigate();

  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isSignupModalOpen, setIsSignupModalOpen] = useState(false);
  const [loginData, setLoginData] = useState({ email: "", password: "" });
  const [signupData, setSignupData] = useState({
    email: "",
    password: "",
    firstName: "",
    lastName: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await login(loginData.email, loginData.password);
      setIsLoginModalOpen(false);
      navigate("/dashboard");
    } catch (error) {
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await signup(signupData);
      setIsSignupModalOpen(false);
      navigate("/dashboard");
      toast.success("Account created successfully!");
    } catch (error) {
      console.error("Signup error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0 flex items-center">
              <Camera className="h-8 w-8 text-nutrisnap-teal" />
              <span className="ml-2 text-xl font-bold text-nutrisnap-charcoal">
                NutriSnap
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-4">
            {/* Public navigation links */}
            <Button
              variant="ghost"
              onClick={() => navigate("/gallery")}
              className="text-nutrisnap-charcoal hover:text-nutrisnap-teal"
            >
              Gallery
            </Button>
            <Button
              variant="ghost"
              onClick={() => navigate("/announcements")}
              className="text-nutrisnap-charcoal hover:text-nutrisnap-teal"
            >
              News
            </Button>

            {isAuthenticated ? (
              <>
                <Button
                  variant="ghost"
                  onClick={() => navigate("/dashboard")}
                  className="text-nutrisnap-charcoal hover:text-nutrisnap-teal"
                >
                  Dashboard
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => navigate("/snap-new")}
                  className="text-nutrisnap-charcoal hover:text-nutrisnap-teal"
                >
                  Snap New
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleLogout}
                  className="text-nutrisnap-charcoal hover:text-nutrisnap-teal"
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={() => setIsLoginModalOpen(true)}
                  className="text-nutrisnap-charcoal hover:text-nutrisnap-teal"
                >
                  Login
                </Button>
                <Button
                  onClick={() => setIsSignupModalOpen(true)}
                  className="bg-nutrisnap-teal hover:bg-nutrisnap-teal/90 text-white"
                >
                  Sign Up
                </Button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="text-nutrisnap-charcoal p-2"
              aria-expanded={mobileMenuOpen}
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-white shadow-lg rounded-b-lg animate-fade-in">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {/* Public navigation links */}
            <Button
              variant="ghost"
              onClick={() => {
                navigate("/gallery");
                setMobileMenuOpen(false);
              }}
              className="w-full justify-start text-nutrisnap-charcoal hover:text-nutrisnap-teal"
            >
              Gallery
            </Button>
            <Button
              variant="ghost"
              onClick={() => {
                navigate("/announcements");
                setMobileMenuOpen(false);
              }}
              className="w-full justify-start text-nutrisnap-charcoal hover:text-nutrisnap-teal"
            >
              News
            </Button>

            {isAuthenticated ? (
              <>
                <Button
                  variant="ghost"
                  onClick={() => {
                    navigate("/dashboard");
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start text-nutrisnap-charcoal hover:text-nutrisnap-teal"
                >
                  Dashboard
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => {
                    navigate("/snap-new");
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start text-nutrisnap-charcoal hover:text-nutrisnap-teal"
                >
                  Snap New
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => {
                    handleLogout();
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start text-nutrisnap-charcoal hover:text-nutrisnap-teal"
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={() => {
                    setIsLoginModalOpen(true);
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start text-nutrisnap-charcoal hover:text-nutrisnap-teal"
                >
                  Login
                </Button>
                <Button
                  onClick={() => {
                    setIsSignupModalOpen(true);
                    setMobileMenuOpen(false);
                  }}
                  className="w-full bg-nutrisnap-teal hover:bg-nutrisnap-teal/90 text-white"
                >
                  Sign Up
                </Button>
              </>
            )}
          </div>
        </div>
      )}

      {/* Login Modal */}
      <Dialog open={isLoginModalOpen} onOpenChange={setIsLoginModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Login to your account</DialogTitle>
            <DialogDescription>
              Enter your credentials to access your NutriSnap account.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
                value={loginData.email}
                onChange={(e) =>
                  setLoginData({ ...loginData, email: e.target.value })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                required
                value={loginData.password}
                onChange={(e) =>
                  setLoginData({ ...loginData, password: e.target.value })
                }
              />
            </div>
            <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2 items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Use demo: <EMAIL> / password
              </p>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Logging in...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <LogIn className="h-4 w-4 mr-2" />
                    Login
                  </span>
                )}
              </Button>
            </div>
            <div className="text-center mt-4">
              <button
                type="button"
                className="text-sm text-nutrisnap-teal hover:underline"
                onClick={() => {
                  setIsLoginModalOpen(false);
                  setIsSignupModalOpen(true);
                }}
              >
                Don't have an account? Sign up
              </button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Signup Modal */}
      <Dialog open={isSignupModalOpen} onOpenChange={setIsSignupModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create an account</DialogTitle>
            <DialogDescription>
              Join NutriSnap and start tracking your nutrition today.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSignup} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  placeholder="John"
                  required
                  value={signupData.firstName}
                  onChange={(e) =>
                    setSignupData({ ...signupData, firstName: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  placeholder="Doe"
                  required
                  value={signupData.lastName}
                  onChange={(e) =>
                    setSignupData({ ...signupData, lastName: e.target.value })
                  }
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="signupEmail">Email</Label>
              <Input
                id="signupEmail"
                type="email"
                placeholder="<EMAIL>"
                required
                value={signupData.email}
                onChange={(e) =>
                  setSignupData({ ...signupData, email: e.target.value })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="signupPassword">Password</Label>
              <Input
                id="signupPassword"
                type="password"
                placeholder="••••••••"
                required
                value={signupData.password}
                onChange={(e) =>
                  setSignupData({ ...signupData, password: e.target.value })
                }
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Creating account...
                </span>
              ) : (
                "Sign Up"
              )}
            </Button>
            <div className="text-center mt-4">
              <button
                type="button"
                className="text-sm text-nutrisnap-teal hover:underline"
                onClick={() => {
                  setIsSignupModalOpen(false);
                  setIsLoginModalOpen(true);
                }}
              >
                Already have an account? Login
              </button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </nav>
  );
};

export default Navbar;
