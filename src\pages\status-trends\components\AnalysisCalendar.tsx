
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Calendar } from "lucide-react";

interface AnalysisCalendarProps {
  calendarData: Record<string, number>;
}

const AnalysisCalendar: React.FC<AnalysisCalendarProps> = ({ calendarData }) => {
  // Helper to render calendar cells
  const renderCalendarCells = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    // Generate month labels
    const monthLabels = [];
    const dates = Object.keys(calendarData).sort();
    if (dates.length > 0) {
      const firstDate = new Date(dates[0]);
      const lastDate = new Date(dates[dates.length - 1]);
      
      for (let m = new Date(firstDate); m <= lastDate; m.setMonth(m.getMonth() + 1)) {
        if (m.getDate() === 1 || m.getTime() === firstDate.getTime()) {
          monthLabels.push({
            month: months[m.getMonth()],
            x: monthLabels.length * 24 // 24px per week
          });
        }
      }
    }
    
    // Generate calendar cells
    const cells = [];
    const daysInWeek = 7;
    const weeksCount = Math.ceil(Object.keys(calendarData).length / daysInWeek);
    
    for (let i = 0; i < weeksCount; i++) {
      for (let j = 0; j < daysInWeek; j++) {
        const index = i * daysInWeek + j;
        const date = Object.keys(calendarData).sort()[index];
        if (!date) continue;
        
        const count = calendarData[date];
        const intensity = count === 0 ? 0 : Math.min(Math.ceil(count / 3 * 4), 4); // 0-4 scale
        
        cells.push(
          <div
            key={date}
            title={`${date}: ${count} entries`}
            className={`w-4 h-4 m-0.5 rounded-md cursor-pointer transition-all duration-200 hover:scale-110 ${
              intensity === 0 
              ? 'bg-gray-200 hover:bg-gray-300' 
              : intensity === 1 
              ? 'bg-nutrisnap-teal/25 hover:bg-nutrisnap-teal/35' 
              : intensity === 2 
              ? 'bg-nutrisnap-teal/50 hover:bg-nutrisnap-teal/60' 
              : intensity === 3 
              ? 'bg-nutrisnap-teal/75 hover:bg-nutrisnap-teal/85'
              : 'bg-nutrisnap-teal hover:bg-nutrisnap-teal/90'
            }`}
          />
        );
      }
    }
    
    return (
      <div className="mt-8 px-4 md:px-6">
        <div className="flex text-xs text-gray-500 mb-2">
          {days.map(day => (
            <div key={day} className="w-5 mr-0.5 font-medium">{day.charAt(0)}</div>
          ))}
        </div>
        <div className="relative">
          <div className="absolute top-0 left-0 text-xs text-gray-500">
            {monthLabels.map((label, i) => (
              <div key={i} className="absolute font-medium" style={{ left: `${label.x}px` }}>
                {label.month}
              </div>
            ))}
          </div>
          <div className="flex flex-wrap mt-6 max-w-3xl">
            {cells}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className="mb-8 shadow-lg border-none overflow-hidden bg-white hover:shadow-xl transition-all duration-300 rounded-xl">
      <CardHeader className="bg-gradient-to-r from-gray-50/80 to-white/70 pb-2 border-b border-gray-100">
        <CardTitle className="flex items-center text-lg font-semibold">
          <Calendar className="mr-3 h-5 w-5 text-nutrisnap-teal" />
          Analysis Frequency
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4 pb-8">
        {renderCalendarCells()}
        
        <div className="mt-10 flex items-center justify-center px-4">
          <span className="text-xs text-gray-600 mr-3 font-medium">Less</span>
          <div className="flex space-x-2">
            <div className="w-4 h-4 bg-gray-200 rounded-md"></div>
            <div className="w-4 h-4 bg-nutrisnap-teal/25 rounded-md"></div>
            <div className="w-4 h-4 bg-nutrisnap-teal/50 rounded-md"></div>
            <div className="w-4 h-4 bg-nutrisnap-teal/75 rounded-md"></div>
            <div className="w-4 h-4 bg-nutrisnap-teal rounded-md"></div>
          </div>
          <span className="text-xs text-gray-600 ml-3 font-medium">More</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default AnalysisCalendar;
