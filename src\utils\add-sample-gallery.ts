// Sample Gallery Items Creator
// This utility helps add sample gallery items for testing

export const sampleGalleryItems = [
  {
    title: "Healthy Breakfast Bowl",
    description: "A nutritious breakfast bowl with fresh fruits, granola, and yogurt",
    category: "food",
    featured: true,
    tags: ["healthy", "breakfast", "fruits", "yogurt"],
    imageUrl: "https://images.unsplash.com/photo-1511690743698-d9d85f2fbf38?w=600&h=400&fit=crop"
  },
  {
    title: "Grilled Salmon Dinner",
    description: "Perfectly grilled salmon with roasted vegetables and quinoa",
    category: "food", 
    featured: true,
    tags: ["salmon", "dinner", "protein", "healthy"],
    imageUrl: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=600&h=400&fit=crop"
  },
  {
    title: "Fresh Garden Salad",
    description: "Mixed greens with cherry tomatoes, cucumber, and balsamic dressing",
    category: "food",
    featured: false,
    tags: ["salad", "vegetables", "fresh", "light"],
    imageUrl: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=600&h=400&fit=crop"
  },
  {
    title: "Smoothie Bowl Recipe",
    description: "Step-by-step guide to making the perfect smoothie bowl",
    category: "recipe",
    featured: true,
    tags: ["smoothie", "recipe", "healthy", "tutorial"],
    imageUrl: "https://images.unsplash.com/photo-1511690656952-34342bb7c2f2?w=600&h=400&fit=crop"
  },
  {
    title: "Nutrition Facts Guide",
    description: "Understanding macronutrients and their importance in your diet",
    category: "nutrition",
    featured: false,
    tags: ["nutrition", "education", "macronutrients", "health"],
    imageUrl: "https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=600&h=400&fit=crop"
  },
  {
    title: "Meal Prep Sunday",
    description: "Tips and tricks for effective meal preparation",
    category: "general",
    featured: false,
    tags: ["meal-prep", "planning", "efficiency", "tips"],
    imageUrl: "https://images.unsplash.com/photo-1455619452474-d2be8b1e70cd?w=600&h=400&fit=crop"
  }
];

// Function to create sample gallery items via API
export const createSampleGalleryItems = async () => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('No authentication token found. Please login first.');
  }

  const results = [];
  
  for (const item of sampleGalleryItems) {
    try {
      console.log(`🔍 Creating sample item: ${item.title}`);
      
      // Create a FormData object
      const formData = new FormData();
      formData.append('title', item.title);
      formData.append('description', item.description);
      formData.append('category', item.category);
      formData.append('featured', item.featured.toString());
      formData.append('tags', item.tags.join(','));
      
      // For demo purposes, we'll use the imageUrl as a reference
      // In a real scenario, you'd upload actual image files
      formData.append('imageUrl', item.imageUrl);
      
      const response = await fetch('http://localhost:5000/api/admin/gallery', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Created: ${item.title}`);
        results.push({ success: true, item: item.title, data });
      } else {
        const errorText = await response.text();
        console.error(`❌ Failed to create ${item.title}:`, response.status, errorText);
        results.push({ success: false, item: item.title, error: `${response.status}: ${errorText}` });
      }
    } catch (error) {
      console.error(`❌ Error creating ${item.title}:`, error);
      results.push({ success: false, item: item.title, error: error.message });
    }
  }
  
  return results;
};

// Function to add sample items button to admin gallery
export const addSampleItemsButton = () => {
  return {
    onClick: async () => {
      if (confirm('This will add 6 sample gallery items. Continue?')) {
        try {
          const results = await createSampleGalleryItems();
          const successful = results.filter(r => r.success).length;
          const failed = results.filter(r => !r.success).length;
          
          alert(`Sample items creation completed!\n✅ Successful: ${successful}\n❌ Failed: ${failed}\n\nCheck console for details.`);
          
          // Refresh the page to show new items
          window.location.reload();
        } catch (error) {
          alert(`Error creating sample items: ${error.message}`);
        }
      }
    }
  };
};
