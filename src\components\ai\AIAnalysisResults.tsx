import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Utensils,
  Zap,
  Activity,
  Wheat,
  Droplets,
  Plus
} from "lucide-react";
import { ProcessedFoodItem, ProcessedNutritionalSummary } from "@/hooks/use-ai-analysis";
import FoodItemEditDialog from "./FoodItemEditDialog";

interface AIAnalysisResultsProps {
  foodItems: ProcessedFoodItem[];
  nutritionalSummary: ProcessedNutritionalSummary;
  overallNotes?: string;
  isLoading?: boolean;
  error?: string | null;
  onRetry?: () => void;
  onEditItem?: (index: number, item: ProcessedFoodItem) => void;
  onAddItem?: (item: ProcessedFoodItem) => void;
  onRemoveItem?: (index: number) => void;
}

const AIAnalysisResults: React.FC<AIAnalysisResultsProps> = ({
  foodItems,
  nutritionalSummary,
  overallNotes,
  isLoading = false,
  error,
  onRetry,
  onEditItem,
  onAddItem,
  onRemoveItem,
}) => {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<ProcessedFoodItem | null>(null);
  const [editingIndex, setEditingIndex] = useState<number>(-1);

  const handleEditClick = (index: number, item: ProcessedFoodItem) => {
    setEditingItem(item);
    setEditingIndex(index);
    setEditDialogOpen(true);
  };

  const handleSaveEdit = (updatedItem: ProcessedFoodItem) => {
    if (onEditItem && editingIndex >= 0) {
      onEditItem(editingIndex, updatedItem);
    }
    setEditDialogOpen(false);
    setEditingItem(null);
    setEditingIndex(-1);
  };

  const handleDeleteItem = () => {
    if (onRemoveItem && editingIndex >= 0) {
      onRemoveItem(editingIndex);
    }
    setEditDialogOpen(false);
    setEditingItem(null);
    setEditingIndex(-1);
  };

  const handleAddNewItem = () => {
    const newItem: ProcessedFoodItem = {
      name: "New Food Item",
      portionSize: "1 serving",
      calories: 100,
      protein: 5,
      carbohydrates: 15,
      fats: 3,
      confidence: 100,
      notes: "Manually added by user",
      userVerified: true,
      userAdded: true,
    };

    if (onAddItem) {
      onAddItem(newItem);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return "bg-green-100 text-green-800 border-green-200";
    if (confidence >= 70) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    return "bg-red-100 text-red-800 border-red-200";
  };

  const getConfidenceIcon = (confidence: number) => {
    if (confidence >= 90) return <CheckCircle className="h-3 w-3" />;
    if (confidence >= 70) return <AlertCircle className="h-3 w-3" />;
    return <AlertCircle className="h-3 w-3" />;
  };

  const nutritionIcons = {
    calories: <Zap className="h-4 w-4 text-orange-500" />,
    protein: <Activity className="h-4 w-4 text-red-500" />,
    carbs: <Wheat className="h-4 w-4 text-amber-500" />,
    fat: <Droplets className="h-4 w-4 text-blue-500" />,
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Analysis Failed</AlertTitle>
        <AlertDescription className="mt-2">
          {error}
          {onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="mt-3 ml-0"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Analysis
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-nutrisnap-teal mr-2"></div>
            Analyzing your food...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>AI Analysis Progress</span>
                <span>Processing...</span>
              </div>
              <Progress value={75} className="h-2" />
            </div>
            <p className="text-sm text-gray-600">
              Our AI is identifying food items and calculating nutritional information...
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Food Items Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Utensils className="h-5 w-5 mr-2 text-nutrisnap-teal" />
            Detected Food Items ({foodItems.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {foodItems.map((item, index) => (
              <div
                key={index}
                className="p-4 bg-gray-50 rounded-lg border hover:shadow-sm transition-shadow"
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <h4 className="font-semibold text-lg">{item.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{item.portionSize}</p>
                  </div>
                  <Badge
                    className={`ml-3 flex items-center gap-1 ${getConfidenceColor(item.confidence)}`}
                  >
                    {getConfidenceIcon(item.confidence)}
                    {Math.round(item.confidence)}% confident
                  </Badge>
                </div>

                {/* Nutritional breakdown for this item */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                  <div className="text-center p-2 bg-white rounded border">
                    <div className="flex items-center justify-center mb-1">
                      {nutritionIcons.calories}
                    </div>
                    <p className="text-sm font-semibold">{item.calories}</p>
                    <p className="text-xs text-gray-500">calories</p>
                  </div>
                  <div className="text-center p-2 bg-white rounded border">
                    <div className="flex items-center justify-center mb-1">
                      {nutritionIcons.protein}
                    </div>
                    <p className="text-sm font-semibold">{item.protein}g</p>
                    <p className="text-xs text-gray-500">protein</p>
                  </div>
                  <div className="text-center p-2 bg-white rounded border">
                    <div className="flex items-center justify-center mb-1">
                      {nutritionIcons.carbs}
                    </div>
                    <p className="text-sm font-semibold">{item.carbohydrates}g</p>
                    <p className="text-xs text-gray-500">carbs</p>
                  </div>
                  <div className="text-center p-2 bg-white rounded border">
                    <div className="flex items-center justify-center mb-1">
                      {nutritionIcons.fat}
                    </div>
                    <p className="text-sm font-semibold">{item.fats}g</p>
                    <p className="text-xs text-gray-500">fat</p>
                  </div>
                </div>

                {/* Notes and actions */}
                {item.notes && (
                  <p className="text-sm text-gray-600 mb-3 italic">
                    Note: {item.notes}
                  </p>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditClick(index, item)}
                  className="text-nutrisnap-teal border-nutrisnap-teal hover:bg-nutrisnap-teal hover:text-white"
                >
                  Edit Item
                </Button>
              </div>
            ))}

            {/* Add Food Item Button */}
            {onAddItem && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={handleAddNewItem}
                  className="w-full flex items-center justify-center text-nutrisnap-teal border-nutrisnap-teal hover:bg-nutrisnap-teal hover:text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Food Item
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Nutritional Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2 text-nutrisnap-teal" />
            Total Nutritional Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
              <div className="flex items-center justify-center mb-2">
                {nutritionIcons.calories}
              </div>
              <p className="text-2xl font-bold text-orange-700">{nutritionalSummary.calories}</p>
              <p className="text-sm text-orange-600">calories</p>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-center justify-center mb-2">
                {nutritionIcons.protein}
              </div>
              <p className="text-2xl font-bold text-red-700">{nutritionalSummary.protein}g</p>
              <p className="text-sm text-red-600">protein</p>
            </div>
            <div className="text-center p-4 bg-amber-50 rounded-lg border border-amber-200">
              <div className="flex items-center justify-center mb-2">
                {nutritionIcons.carbs}
              </div>
              <p className="text-2xl font-bold text-amber-700">{nutritionalSummary.carbs}g</p>
              <p className="text-sm text-amber-600">carbs</p>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-center mb-2">
                {nutritionIcons.fat}
              </div>
              <p className="text-2xl font-bold text-blue-700">{nutritionalSummary.fat}g</p>
              <p className="text-sm text-blue-600">fat</p>
            </div>
          </div>

          {/* Additional nutrients */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-gray-50 rounded border">
              <p className="text-lg font-semibold">{nutritionalSummary.fiber}g</p>
              <p className="text-xs text-gray-500">fiber</p>
            </div>
            <div className="p-3 bg-gray-50 rounded border">
              <p className="text-lg font-semibold">{nutritionalSummary.sugar}g</p>
              <p className="text-xs text-gray-500">sugar</p>
            </div>
            <div className="p-3 bg-gray-50 rounded border">
              <p className="text-lg font-semibold">{nutritionalSummary.sodium}mg</p>
              <p className="text-xs text-gray-500">sodium</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overall Notes */}
      {overallNotes && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>AI Analysis Notes</AlertTitle>
          <AlertDescription>{overallNotes}</AlertDescription>
        </Alert>
      )}

      {/* Edit Dialog */}
      <FoodItemEditDialog
        isOpen={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        foodItem={editingItem}
        onSave={handleSaveEdit}
        onDelete={onRemoveItem ? handleDeleteItem : undefined}
      />
    </div>
  );
};

export default AIAnalysisResults;
