/**
 * Frontend number utility functions for handling floating-point precision
 */

/**
 * Round a number to specified decimal places
 * @param num - The number to round
 * @param decimals - Number of decimal places (default: 1)
 * @returns Properly rounded number
 */
export function roundToDecimals(num: number, decimals: number = 1): number {
  if (typeof num !== 'number' || isNaN(num)) {
    return 0;
  }
  
  const factor = Math.pow(10, decimals);
  return Math.round((num + Number.EPSILON) * factor) / factor;
}

/**
 * Round nutritional values consistently
 * @param value - The nutritional value to round
 * @returns Rounded value (1 decimal place for precision)
 */
export function roundNutritionalValue(value: number): number {
  return roundToDecimals(value, 1);
}

/**
 * Round calorie values (typically whole numbers)
 * @param calories - The calorie value to round
 * @returns Rounded calories (whole number)
 */
export function roundCalories(calories: number): number {
  return Math.round(calories || 0);
}

/**
 * Format nutritional value for display
 * @param value - The value to format
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted string
 */
export function formatNutritionalValue(value: number, decimals: number = 1): string {
  const rounded = roundToDecimals(value, decimals);
  
  // Remove trailing zeros for cleaner display
  if (decimals > 0) {
    return parseFloat(rounded.toFixed(decimals)).toString();
  }
  
  return rounded.toString();
}

/**
 * Format number for display with proper rounding
 * Examples:
 * - 178.89999999999998 → "178.9"
 * - 178.0 → "178"
 * - 178.12345 → "178.1"
 */
export function formatDisplayNumber(value: number | undefined | null, maxDecimals: number = 1): string {
  if (typeof value !== 'number' || isNaN(value) || value === null || value === undefined) {
    return '0';
  }
  
  const rounded = roundToDecimals(value, maxDecimals);
  
  // Remove unnecessary trailing zeros
  return parseFloat(rounded.toFixed(maxDecimals)).toString();
}

/**
 * Format percentage values
 * @param value - The percentage value (0-100)
 * @param decimals - Number of decimal places (default: 0)
 * @returns Formatted percentage string
 */
export function formatPercentage(value: number | undefined | null, decimals: number = 0): string {
  if (typeof value !== 'number' || isNaN(value) || value === null || value === undefined) {
    return '0%';
  }
  
  const rounded = roundToDecimals(value, decimals);
  return `${parseFloat(rounded.toFixed(decimals))}%`;
}

/**
 * Format weight values (grams, kg, etc.)
 * @param value - The weight value
 * @param unit - The unit (default: 'g')
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted weight string
 */
export function formatWeight(value: number | undefined | null, unit: string = 'g', decimals: number = 1): string {
  if (typeof value !== 'number' || isNaN(value) || value === null || value === undefined) {
    return `0${unit}`;
  }
  
  const formatted = formatDisplayNumber(value, decimals);
  return `${formatted}${unit}`;
}

/**
 * Format calorie values specifically
 * @param calories - The calorie value
 * @returns Formatted calorie string
 */
export function formatCalories(calories: number | undefined | null): string {
  if (typeof calories !== 'number' || isNaN(calories) || calories === null || calories === undefined) {
    return '0';
  }
  
  return Math.round(calories).toString();
}

/**
 * Safe number parsing with fallback
 * @param value - Value to parse (string or number)
 * @param fallback - Fallback value if parsing fails (default: 0)
 * @returns Parsed number or fallback
 */
export function safeParseNumber(value: string | number | undefined | null, fallback: number = 0): number {
  if (typeof value === 'number' && !isNaN(value)) {
    return value;
  }
  
  if (typeof value === 'string') {
    const parsed = parseFloat(value.replace(/[^\d.-]/g, ''));
    return isNaN(parsed) ? fallback : parsed;
  }
  
  return fallback;
}

/**
 * Format nutritional data object with consistent formatting
 * @param nutrition - Nutrition data object
 * @returns Formatted nutrition object
 */
export function formatNutritionData(nutrition: {
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
}): {
  calories: string;
  protein: string;
  carbs: string;
  fat: string;
  fiber: string;
  sugar: string;
  sodium: string;
} {
  return {
    calories: formatCalories(nutrition.calories),
    protein: formatWeight(nutrition.protein, 'g'),
    carbs: formatWeight(nutrition.carbs, 'g'),
    fat: formatWeight(nutrition.fat, 'g'),
    fiber: formatWeight(nutrition.fiber, 'g'),
    sugar: formatWeight(nutrition.sugar, 'g'),
    sodium: formatWeight(nutrition.sodium, 'mg', 0), // Sodium typically shown as whole numbers
  };
}
