import apiClient from '@/lib/api-client';
import { FoodAnalysis, FoodItem } from '@/types/meal';
import { AxiosError } from 'axios';

export interface FoodAnalysisResponse {
  success: boolean;
  foodAnalysis: FoodAnalysis;
}

export interface FoodAnalysesResponse {
  success: boolean;
  count: number;
  foodAnalyses: FoodAnalysis[];
}

export interface UploadFoodItem {
  foodItem: string;
  confidence: number;
  boundingBox: { x: number; y: number; width: number; height: number };
  quantityGrams: number;
  commonPortions: string[];
  selectedPortion: string;
  userVerified: boolean;
  userAdded?: boolean;
  // Additional nutrition data for backend processing
  calories?: number;
  protein?: number;
  carbohydrates?: number;
  fats?: number;
  notes?: string;
}

export interface UploadFoodAnalysisData {
  image: File;
  mealCategory: string;
  mealDateTime: string;
  userNotes?: string;
  recognitionResults: UploadFoodItem[];
  nutritionalSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
  };
}

export interface AIAnalysisResponse {
  success: boolean;
  message: string;
  data?: {
    analysis: {
      food_items: Array<{
        name: string;
        portion_size: string;
        calories: string;
        protein: string;
        carbohydrates: string;
        fats: string;
        confidence: string;
        notes?: string;
      }>;
      totals: {
        total_calories: string;
        total_protein: string;
        total_carbohydrates: string;
        total_fats: string;
      };
      overall_notes?: string;
    };
    metadata: {
      filename: string;
      fileSize: number;
      mimeType: string;
      analyzedAt: string;
      userId: string;
    };
  };
  error?: string;
}

export interface SupportedFormatsResponse {
  success: boolean;
  data: {
    formats: Array<{
      extension: string;
      mimeType: string;
      description: string;
    }>;
    maxFileSize: string;
    recommendations: string[];
  };
}

// File validation utility
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/avif'];
  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!supportedFormats.includes(file.type)) {
    return {
      isValid: false,
      error: 'Unsupported file format. Please use JPEG, PNG, WebP, or AVIF.'
    };
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File too large. Maximum size is 10MB.'
    };
  }

  return { isValid: true };
};

const FoodAnalysisService = {
  // Validate image file before analysis
  validateImage: validateImageFile,

  // Analyze food image using AI (Gemini)
  analyzeImage: async (image: File): Promise<AIAnalysisResponse> => {
    // Validate file before sending
    const validation = validateImageFile(image);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    const formData = new FormData();
    formData.append('image', image);

    const response = await apiClient.post<AIAnalysisResponse>('/food-analysis/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // 60 second timeout for AI analysis
    });

    return response.data;
  },

  // Get supported image formats
  getSupportedFormats: async (): Promise<SupportedFormatsResponse> => {
    const response = await apiClient.get<SupportedFormatsResponse>('/food-analysis/supported-formats');
    return response.data;
  },

  // Get all food analyses for the current user
  getAllAnalyses: async (params?: {
    startDate?: string;
    endDate?: string;
    mealCategory?: string;
  }): Promise<FoodAnalysesResponse> => {
    const response = await apiClient.get<FoodAnalysesResponse>('/food-analysis', {
      params,
    });
    return response.data;
  },

  // Get a specific food analysis by ID
  getAnalysisById: async (id: string): Promise<FoodAnalysisResponse> => {
    const response = await apiClient.get<FoodAnalysisResponse>(`/food-analysis/${id}`);
    return response.data;
  },

  // Upload a new food analysis (save meal)
  uploadAnalysis: async (data: UploadFoodAnalysisData): Promise<FoodAnalysisResponse> => {
    try {
      // Convert image to base64 for JSON payload
      const imageBase64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(data.image);
      });

      // Convert recognitionResults to analysisData format expected by backend
      const analysisData = {
        food_items: data.recognitionResults.map(item => ({
          name: item.foodItem || item.name || 'Unknown Food',
          portion_size: item.selectedPortion || item.portionSize || '1 serving',
          calories: item.calories?.toString() || '0',
          protein: item.protein?.toString() || '0',
          carbohydrates: item.carbohydrates?.toString() || '0',
          fats: item.fats?.toString() || '0',
          confidence: item.confidence?.toString() || '100',
          notes: item.notes || ''
        })),
        totals: {
          total_calories: data.nutritionalSummary.calories.toString(),
          total_protein: data.nutritionalSummary.protein.toString(),
          total_carbohydrates: data.nutritionalSummary.carbs.toString(),
          total_fats: data.nutritionalSummary.fat.toString()
        },
        overall_notes: data.userNotes || ''
      };

      const requestData = {
        imageFile: imageBase64,
        mealCategory: data.mealCategory,
        mealDateTime: data.mealDateTime,
        userNotes: data.userNotes || '',
        analysisData: analysisData
      };

      console.log('📤 Sending save meal request to /api/food-analysis/save-meal (JSON):', {
        mealCategory: data.mealCategory,
        mealDateTime: data.mealDateTime,
        userNotes: data.userNotes,
        foodItemsCount: analysisData.food_items.length,
        totalCalories: analysisData.totals.total_calories,
        imageSize: data.image.size
      });

      const response = await apiClient.post<FoodAnalysisResponse>('/food-analysis/save-meal', requestData, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 second timeout
      });

      console.log('✅ Save meal response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error('❌ Save meal error:', error);

      // Type guard for axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as AxiosError<{ message?: string; error?: string }>;
        console.error('❌ Error response:', axiosError.response?.data);
        console.error('❌ Error status:', axiosError.response?.status);
        console.error('❌ Error headers:', axiosError.response?.headers);

        // Provide more specific error message
        if (axiosError.response?.data?.message) {
          throw new Error(axiosError.response.data.message);
        } else if (axiosError.response?.data?.error) {
          throw new Error(axiosError.response.data.error);
        }
      }

      throw error;
    }
  },

  // Update an existing food analysis
  updateAnalysis: async (
    id: string,
    data: {
      mealCategory?: string;
      mealDateTime?: string;
      userNotes?: string;
      recognitionResults?: FoodItem[];
      nutritionalSummary?: {
        calories: number;
        protein: number;
        carbs: number;
        fat: number;
        fiber: number;
        sugar: number;
        sodium: number;
      };
    }
  ): Promise<FoodAnalysisResponse> => {
    const response = await apiClient.put<FoodAnalysisResponse>(`/food-analysis/${id}`, data);
    return response.data;
  },

  // Delete a food analysis
  deleteAnalysis: async (id: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete<{ success: boolean; message: string }>(
      `/food-analysis/${id}`
    );
    return response.data;
  },
};

export default FoodAnalysisService;
