import apiClient from '@/lib/api-client';
import { FoodAnalysis, FoodItem } from '@/types/meal';

export interface FoodAnalysisResponse {
  success: boolean;
  foodAnalysis: FoodAnalysis;
}

export interface FoodAnalysesResponse {
  success: boolean;
  count: number;
  foodAnalyses: FoodAnalysis[];
}

export interface UploadFoodAnalysisData {
  image: File;
  mealCategory: string;
  mealDateTime: string;
  userNotes?: string;
  recognitionResults: FoodItem[];
  nutritionalSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
  };
}

const FoodAnalysisService = {
  // Get all food analyses for the current user
  getAllAnalyses: async (params?: {
    startDate?: string;
    endDate?: string;
    mealCategory?: string;
  }): Promise<FoodAnalysesResponse> => {
    const response = await apiClient.get<FoodAnalysesResponse>('/food-analysis', {
      params,
    });
    return response.data;
  },

  // Get a specific food analysis by ID
  getAnalysisById: async (id: string): Promise<FoodAnalysisResponse> => {
    const response = await apiClient.get<FoodAnalysisResponse>(`/food-analysis/${id}`);
    return response.data;
  },

  // Upload a new food analysis
  uploadAnalysis: async (data: UploadFoodAnalysisData): Promise<FoodAnalysisResponse> => {
    const formData = new FormData();
    formData.append('image', data.image);
    formData.append('mealCategory', data.mealCategory);
    formData.append('mealDateTime', data.mealDateTime);
    
    if (data.userNotes) {
      formData.append('userNotes', data.userNotes);
    }
    
    formData.append('recognitionResults', JSON.stringify(data.recognitionResults));
    formData.append('nutritionalSummary', JSON.stringify(data.nutritionalSummary));

    const response = await apiClient.post<FoodAnalysisResponse>('/food-analysis/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  },

  // Update an existing food analysis
  updateAnalysis: async (
    id: string,
    data: {
      mealCategory?: string;
      mealDateTime?: string;
      userNotes?: string;
      recognitionResults?: FoodItem[];
      nutritionalSummary?: {
        calories: number;
        protein: number;
        carbs: number;
        fat: number;
        fiber: number;
        sugar: number;
        sodium: number;
      };
    }
  ): Promise<FoodAnalysisResponse> => {
    const response = await apiClient.put<FoodAnalysisResponse>(`/food-analysis/${id}`, data);
    return response.data;
  },

  // Delete a food analysis
  deleteAnalysis: async (id: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete<{ success: boolean; message: string }>(
      `/food-analysis/${id}`
    );
    return response.data;
  },
};

export default FoodAnalysisService;
