import apiClient from '@/lib/api-client';
import { FoodAnalysis, FoodItem } from '@/types/meal';

export interface FoodAnalysisResponse {
  success: boolean;
  foodAnalysis: FoodAnalysis;
}

export interface FoodAnalysesResponse {
  success: boolean;
  count: number;
  foodAnalyses: FoodAnalysis[];
}

export interface UploadFoodItem {
  foodItem: string;
  confidence: number;
  boundingBox: { x: number; y: number; width: number; height: number };
  quantityGrams: number;
  commonPortions: string[];
  selectedPortion: string;
  userVerified: boolean;
  userAdded?: boolean;
  // Additional nutrition data for backend processing
  calories?: number;
  protein?: number;
  carbohydrates?: number;
  fats?: number;
  notes?: string;
}

export interface UploadFoodAnalysisData {
  image: File;
  mealCategory: string;
  mealDateTime: string;
  userNotes?: string;
  recognitionResults: UploadFoodItem[];
  nutritionalSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
  };
}

export interface AIAnalysisResponse {
  success: boolean;
  message: string;
  data?: {
    analysis: {
      food_items: Array<{
        name: string;
        portion_size: string;
        calories: string;
        protein: string;
        carbohydrates: string;
        fats: string;
        confidence: string;
        notes?: string;
      }>;
      totals: {
        total_calories: string;
        total_protein: string;
        total_carbohydrates: string;
        total_fats: string;
      };
      overall_notes?: string;
    };
    metadata: {
      filename: string;
      fileSize: number;
      mimeType: string;
      analyzedAt: string;
      userId: string;
    };
  };
  error?: string;
}

export interface SupportedFormatsResponse {
  success: boolean;
  data: {
    formats: Array<{
      extension: string;
      mimeType: string;
      description: string;
    }>;
    maxFileSize: string;
    recommendations: string[];
  };
}

// File validation utility
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/avif'];
  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!supportedFormats.includes(file.type)) {
    return {
      isValid: false,
      error: 'Unsupported file format. Please use JPEG, PNG, WebP, or AVIF.'
    };
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File too large. Maximum size is 10MB.'
    };
  }

  return { isValid: true };
};

const FoodAnalysisService = {
  // Validate image file before analysis
  validateImage: validateImageFile,

  // Analyze food image using AI (Gemini)
  analyzeImage: async (image: File): Promise<AIAnalysisResponse> => {
    // Validate file before sending
    const validation = validateImageFile(image);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    const formData = new FormData();
    formData.append('image', image);

    const response = await apiClient.post<AIAnalysisResponse>('/food-analysis/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // 60 second timeout for AI analysis
    });

    return response.data;
  },

  // Get supported image formats
  getSupportedFormats: async (): Promise<SupportedFormatsResponse> => {
    const response = await apiClient.get<SupportedFormatsResponse>('/food-analysis/supported-formats');
    return response.data;
  },

  // Get all food analyses for the current user
  getAllAnalyses: async (params?: {
    startDate?: string;
    endDate?: string;
    mealCategory?: string;
  }): Promise<FoodAnalysesResponse> => {
    const response = await apiClient.get<FoodAnalysesResponse>('/food-analysis', {
      params,
    });
    return response.data;
  },

  // Get a specific food analysis by ID
  getAnalysisById: async (id: string): Promise<FoodAnalysisResponse> => {
    const response = await apiClient.get<FoodAnalysisResponse>(`/food-analysis/${id}`);
    return response.data;
  },

  // Upload a new food analysis (save meal)
  uploadAnalysis: async (data: UploadFoodAnalysisData): Promise<FoodAnalysisResponse> => {
    try {
      // Convert image to base64 for backend compatibility
      const imageBase64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          // Remove data:image/jpeg;base64, prefix
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(data.image);
      });

      // Prepare payload for backend
      const payload = {
        image: imageBase64,
        mealCategory: data.mealCategory,
        mealDateTime: data.mealDateTime,
        userNotes: data.userNotes || '',
        recognitionResults: data.recognitionResults,
        nutritionalSummary: data.nutritionalSummary
      };

      console.log('📤 Sending save meal request:', {
        mealCategory: payload.mealCategory,
        mealDateTime: payload.mealDateTime,
        userNotes: payload.userNotes,
        recognitionResultsCount: payload.recognitionResults.length,
        nutritionalSummary: payload.nutritionalSummary,
        imageSize: payload.image.length
      });

      const response = await apiClient.post<FoodAnalysisResponse>('/food-analysis', payload, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 second timeout
      });

      console.log('✅ Save meal response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Save meal error:', error);
      throw error;
    }
  },

  // Update an existing food analysis
  updateAnalysis: async (
    id: string,
    data: {
      mealCategory?: string;
      mealDateTime?: string;
      userNotes?: string;
      recognitionResults?: FoodItem[];
      nutritionalSummary?: {
        calories: number;
        protein: number;
        carbs: number;
        fat: number;
        fiber: number;
        sugar: number;
        sodium: number;
      };
    }
  ): Promise<FoodAnalysisResponse> => {
    const response = await apiClient.put<FoodAnalysisResponse>(`/food-analysis/${id}`, data);
    return response.data;
  },

  // Delete a food analysis
  deleteAnalysis: async (id: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete<{ success: boolean; message: string }>(
      `/food-analysis/${id}`
    );
    return response.data;
  },
};

export default FoodAnalysisService;
