import apiClient from '@/lib/api-client';

export interface Announcement {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error';
  priority: 'low' | 'medium' | 'high';
  active: boolean;
  startDate?: string;
  endDate?: string;
  targetRoles: string[];
  createdBy: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface PublicAnnouncement {
  id: string;
  title: string;
  content: string;
  category: 'feature' | 'event' | 'maintenance' | 'update' | 'other';
  importance: 'low' | 'medium' | 'high';
  publishDate: string;
  expiryDate: string;
  createdAt: string;
}

export interface AnnouncementsResponse {
  success: boolean;
  data: Announcement[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface PublicAnnouncementsResponse {
  success: boolean;
  data: PublicAnnouncement[];
}

export interface AnnouncementResponse {
  success: boolean;
  data: Announcement;
}

const AnnouncementsService = {
  // Get active public announcements
  getActiveAnnouncements: async (params?: {
    page?: number;
    limit?: number;
    category?: string;
    importance?: string;
  }): Promise<{ announcements: PublicAnnouncement[]; pagination?: any }> => {
    const response = await apiClient.get('/announcements', { params });

    // Transform _id to id for each announcement
    const transformedAnnouncements = (response.data.announcements || []).map((announcement: any) => ({
      ...announcement,
      id: announcement._id
    }));

    return {
      announcements: transformedAnnouncements,
      pagination: response.data.pagination
    };
  },

  // Get all announcements (admin only)
  getAllAnnouncements: async (params?: {
    page?: number;
    limit?: number;
    active?: boolean;
    type?: string;
    priority?: string;
  }): Promise<AnnouncementsResponse> => {
    const response = await apiClient.get<AnnouncementsResponse>('/announcements/all', { params });
    return response.data;
  },

  // Get announcement by ID (admin only)
  getAnnouncementById: async (id: string): Promise<Announcement> => {
    const response = await apiClient.get<AnnouncementResponse>(`/announcements/${id}`);
    return response.data.data;
  },

  // Create a new announcement (admin only)
  createAnnouncement: async (data: {
    title: string;
    content: string;
    type: 'info' | 'warning' | 'success' | 'error';
    priority: 'low' | 'medium' | 'high';
    active?: boolean;
    startDate?: string;
    endDate?: string;
    targetRoles?: string[];
  }): Promise<Announcement> => {
    const response = await apiClient.post<AnnouncementResponse>('/announcements', data);
    return response.data.data;
  },

  // Update an announcement (admin only)
  updateAnnouncement: async (
    id: string,
    data: {
      title?: string;
      content?: string;
      type?: 'info' | 'warning' | 'success' | 'error';
      priority?: 'low' | 'medium' | 'high';
      active?: boolean;
      startDate?: string;
      endDate?: string;
      targetRoles?: string[];
    }
  ): Promise<Announcement> => {
    const response = await apiClient.put<AnnouncementResponse>(`/announcements/${id}`, data);
    return response.data.data;
  },

  // Delete an announcement (admin only)
  deleteAnnouncement: async (id: string): Promise<void> => {
    await apiClient.delete(`/announcements/${id}`);
  },
};

export default AnnouncementsService;
