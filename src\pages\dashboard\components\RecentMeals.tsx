
import React from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import FoodCard from "@/components/food/FoodCard";
import { mockFoodAnalyses } from "@/data/mockData";

type RecentMealsProps = {
  meals: typeof mockFoodAnalyses;
};

const RecentMeals: React.FC<RecentMealsProps> = ({ meals }) => {
  const navigate = useNavigate();

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-nutrisnap-charcoal">
          Recent Meals
        </h2>
        <Button
          variant="ghost"
          className="text-nutrisnap-teal"
          onClick={() => navigate("/meal-history")}
        >
          View All Meals
          <ChevronRight className="ml-1 h-4 w-4" />
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {meals.slice(0, 6).map((meal) => (
          <FoodCard
            key={meal._id || meal.id}
            id={meal._id || meal.id}
            imageUrl={meal.imageUrl}
            title={meal.userNotes}
            mealCategory={meal.mealCategory}
            date={meal.mealDateTime}
            calories={meal.nutritionalSummary.calories}
            onClick={() => navigate(`/meal-details/${meal._id || meal.id}`)}
          />
        ))}
      </div>
    </div>
  );
};

export default RecentMeals;
