
import React from "react";
import NutritionCard from "@/components/nutrition/NutritionCard";
import { useAuth } from "@/contexts/AuthContext";
import { useFoodAnalyses } from "@/hooks/use-food-analysis";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const NutritionSummary = () => {
  const { user } = useAuth();
  const [timeRange, setTimeRange] = React.useState("daily");
  const [summaryData, setSummaryData] = React.useState({
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    sodium: 0,
  });

  // Calculate date range based on timeRange
  const getDateRange = () => {
    const now = new Date();
    const startDate = new Date();

    switch (timeRange) {
      case "daily":
        startDate.setHours(0, 0, 0, 0);
        break;
      case "weekly":
        startDate.setDate(now.getDate() - 7);
        break;
      case "monthly":
        startDate.setDate(now.getDate() - 30);
        break;
      default:
        startDate.setHours(0, 0, 0, 0);
    }

    return {
      startDate: startDate.toISOString(),
      endDate: now.toISOString()
    };
  };

  const dateRange = getDateRange();

  // Fetch food analyses with date range
  const { data, isLoading, error } = useFoodAnalyses({
    startDate: dateRange.startDate,
    endDate: dateRange.endDate
  });

  React.useEffect(() => {
    if (data?.foodAnalyses) {
      // Sum up all nutritional information from real data
      const totals = data.foodAnalyses.reduce(
        (acc, meal) => {
          acc.calories += meal.nutritionalSummary.calories;
          acc.protein += meal.nutritionalSummary.protein;
          acc.carbs += meal.nutritionalSummary.carbs;
          acc.fat += meal.nutritionalSummary.fat;
          acc.fiber += meal.nutritionalSummary.fiber;
          acc.sugar += meal.nutritionalSummary.sugar;
          acc.sodium += meal.nutritionalSummary.sodium;
          return acc;
        },
        {
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0,
          sugar: 0,
          sodium: 0,
        }
      );

      setSummaryData(totals);
    }
  }, [data, timeRange]);

  // Get nutritional goals from user preferences or set defaults
  const nutritionGoals = user?.preferences.nutritionGoals || {
    calories: 2000,
    protein: 120,
    carbs: 250,
    fat: 70,
  };

  // Calculate nutrition goal multipliers
  const getGoalMultiplier = () => {
    if (timeRange === "weekly") return 7;
    if (timeRange === "monthly") return 30;
    return 1;
  };

  const goalMultiplier = getGoalMultiplier();

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen bg-gray-50">
        <div className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-nutrisnap-charcoal">
                  Nutrition Summary
                </h1>
                <p className="text-gray-500 mt-1">
                  Loading your nutritional data...
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="h-32 bg-gray-200 rounded-lg animate-pulse" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex min-h-screen bg-gray-50">
        <div className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Failed to load nutrition data
              </h3>
              <p className="text-gray-500">
                Please try again later or contact support if the problem persists.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-nutrisnap-charcoal">
                Nutrition Summary
              </h1>
              <p className="text-gray-500 mt-1">
                Detailed breakdown of your nutritional intake
              </p>
            </div>
            <div className="w-48">
              <Select
                value={timeRange}
                onValueChange={(value) => setTimeRange(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <Card>
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-nutrisnap-teal">
                      {data?.foodAnalyses?.length || 0}
                    </p>
                    <p className="text-sm text-gray-500">
                      Meals {timeRange === "daily" ? "today" : `this ${timeRange.slice(0, -2)}`}
                    </p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {Math.round((summaryData.calories / (nutritionGoals.calories * goalMultiplier)) * 100)}%
                    </p>
                    <p className="text-sm text-gray-500">Calorie goal progress</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      {timeRange === "daily" ? "Today" : timeRange === "weekly" ? "7 Days" : "30 Days"}
                    </p>
                    <p className="text-sm text-gray-500">Time period</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Macronutrients */}
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4">Macronutrients</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <NutritionCard
                data={{
                  label: "Calories",
                  value: summaryData.calories,
                  unit: "kcal",
                  goal: nutritionGoals.calories * goalMultiplier,
                  color: "blue",
                }}
              />
              <NutritionCard
                data={{
                  label: "Protein",
                  value: summaryData.protein,
                  unit: "g",
                  goal: nutritionGoals.protein * goalMultiplier,
                  color: "red",
                }}
              />
              <NutritionCard
                data={{
                  label: "Carbs",
                  value: summaryData.carbs,
                  unit: "g",
                  goal: nutritionGoals.carbs * goalMultiplier,
                  color: "green",
                }}
              />
              <NutritionCard
                data={{
                  label: "Fat",
                  value: summaryData.fat,
                  unit: "g",
                  goal: nutritionGoals.fat * goalMultiplier,
                  color: "yellow",
                }}
              />
            </div>
          </div>

          {/* Micronutrients */}
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4">Micronutrients</h2>
            <Card>
              <CardHeader>
                <CardTitle>Details</CardTitle>
                <CardDescription>
                  Breakdown of important micronutrients in your diet
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">Fiber</span>
                      <span className="text-sm text-gray-500">
                        {summaryData.fiber}g / {25 * goalMultiplier}g
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{
                          width: `${Math.min(
                            (summaryData.fiber / (25 * goalMultiplier)) * 100,
                            100
                          )}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">Sugar</span>
                      <span className="text-sm text-gray-500">
                        {summaryData.sugar}g / {30 * goalMultiplier}g
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-orange-500 h-2 rounded-full"
                        style={{
                          width: `${Math.min(
                            (summaryData.sugar / (30 * goalMultiplier)) * 100,
                            100
                          )}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">Sodium</span>
                      <span className="text-sm text-gray-500">
                        {summaryData.sodium}mg / {2300 * goalMultiplier}mg
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{
                          width: `${Math.min(
                            (summaryData.sodium / (2300 * goalMultiplier)) * 100,
                            100
                          )}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Nutrition Breakdown */}
          <div>
            <h2 className="text-xl font-bold mb-4">Nutrition Breakdown</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Calorie Sources</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-60 flex items-center justify-center">
                    <div className="w-40 h-40 rounded-full border-8 border-gray-200 relative">
                      {/* Protein */}
                      <div
                        className="absolute inset-0 rounded-full bg-red-500"
                        style={{
                          clipPath: `polygon(50% 50%, 50% 0%, ${
                            50 + 50 * Math.cos((summaryData.protein * 4 * 360) / summaryData.calories * (Math.PI / 180))
                          }% ${
                            50 - 50 * Math.sin((summaryData.protein * 4 * 360) / summaryData.calories * (Math.PI / 180))
                          }%)`,
                        }}
                      ></div>
                      {/* Carbs */}
                      <div
                        className="absolute inset-0 rounded-full bg-green-500"
                        style={{
                          clipPath: `polygon(50% 50%, ${
                            50 + 50 * Math.cos((summaryData.protein * 4 * 360) / summaryData.calories * (Math.PI / 180))
                          }% ${
                            50 - 50 * Math.sin((summaryData.protein * 4 * 360) / summaryData.calories * (Math.PI / 180))
                          }%, ${
                            50 + 50 * Math.cos(((summaryData.protein * 4 + summaryData.carbs * 4) * 360) / summaryData.calories * (Math.PI / 180))
                          }% ${
                            50 - 50 * Math.sin(((summaryData.protein * 4 + summaryData.carbs * 4) * 360) / summaryData.calories * (Math.PI / 180))
                          }%)`,
                        }}
                      ></div>
                      {/* Fat */}
                      <div
                        className="absolute inset-0 rounded-full bg-yellow-500"
                        style={{
                          clipPath: `polygon(50% 50%, ${
                            50 + 50 * Math.cos(((summaryData.protein * 4 + summaryData.carbs * 4) * 360) / summaryData.calories * (Math.PI / 180))
                          }% ${
                            50 - 50 * Math.sin(((summaryData.protein * 4 + summaryData.carbs * 4) * 360) / summaryData.calories * (Math.PI / 180))
                          }%, 50% 0%)`,
                        }}
                      ></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="bg-white rounded-full w-24 h-24 flex items-center justify-center shadow-inner">
                          <span className="text-xl font-bold">
                            {summaryData.calories}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 mt-4">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-red-500 rounded mr-2"></div>
                      <div>
                        <p className="text-sm font-medium">Protein</p>
                        <p className="text-xs text-gray-500">
                          {Math.round((summaryData.protein * 4 * 100) / summaryData.calories)}%
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
                      <div>
                        <p className="text-sm font-medium">Carbs</p>
                        <p className="text-xs text-gray-500">
                          {Math.round((summaryData.carbs * 4 * 100) / summaryData.calories)}%
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
                      <div>
                        <p className="text-sm font-medium">Fat</p>
                        <p className="text-xs text-gray-500">
                          {Math.round((summaryData.fat * 9 * 100) / summaryData.calories)}%
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Meal Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-60 flex items-center justify-center">
                    {/* Simple bar chart for meal distribution */}
                    <div className="w-full flex items-end h-full space-x-6 justify-around pt-10">
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-yellow-400 rounded-t-md" style={{ height: '100px' }}></div>
                        <p className="text-xs mt-2">Breakfast</p>
                        <p className="text-sm font-semibold">25%</p>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-green-400 rounded-t-md" style={{ height: '150px' }}></div>
                        <p className="text-xs mt-2">Lunch</p>
                        <p className="text-sm font-semibold">35%</p>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-blue-400 rounded-t-md" style={{ height: '180px' }}></div>
                        <p className="text-xs mt-2">Dinner</p>
                        <p className="text-sm font-semibold">40%</p>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-purple-400 rounded-t-md" style={{ height: '80px' }}></div>
                        <p className="text-xs mt-2">Snacks</p>
                        <p className="text-sm font-semibold">20%</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NutritionSummary;
