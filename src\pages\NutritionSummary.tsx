
import React from "react";
import NutritionCard from "@/components/nutrition/NutritionCard";
import { useAuth } from "@/contexts/AuthContext";
import { useFoodAnalyses } from "@/hooks/use-food-analysis";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, ResponsiveContainer } from 'recharts';

const NutritionSummary = () => {
  const { user } = useAuth();
  const [timeRange, setTimeRange] = React.useState("daily");
  const [summaryData, setSummaryData] = React.useState({
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    sodium: 0,
  });

  const [mealDistribution, setMealDistribution] = React.useState({
    breakfast: 0,
    lunch: 0,
    dinner: 0,
    snacks: 0,
  });

  // Calculate date range based on timeRange - use state to prevent infinite re-renders
  const [dateRange, setDateRange] = React.useState(() => {
    const now = new Date();
    const today = now.toISOString().split('T')[0]; // YYYY-MM-DD format

    return {
      startDate: `${today}T00:00:00Z`,
      endDate: `${today}T23:59:59Z`
    };
  });

  // Update date range when timeRange changes
  React.useEffect(() => {
    const now = new Date();
    const today = now.toISOString().split('T')[0]; // YYYY-MM-DD format

    switch (timeRange) {
      case "daily":
        setDateRange({
          startDate: `${today}T00:00:00Z`,
          endDate: `${today}T23:59:59Z`
        });
        break;
      case "weekly":
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - 6);
        setDateRange({
          startDate: `${weekStart.toISOString().split('T')[0]}T00:00:00Z`,
          endDate: `${today}T23:59:59Z`
        });
        break;
      case "monthly":
        const monthStart = new Date(now);
        monthStart.setDate(now.getDate() - 29);
        setDateRange({
          startDate: `${monthStart.toISOString().split('T')[0]}T00:00:00Z`,
          endDate: `${today}T23:59:59Z`
        });
        break;
      default:
        setDateRange({
          startDate: `${today}T00:00:00Z`,
          endDate: `${today}T23:59:59Z`
        });
    }
  }, [timeRange]);

  // Fetch food analyses with date range
  const { data, isLoading, error } = useFoodAnalyses(dateRange);

  React.useEffect(() => {
    if (data?.foodAnalyses) {
      // Sum up all nutritional information from real data
      const totals = data.foodAnalyses.reduce(
        (acc, meal) => {
          acc.calories += meal.nutritionalSummary.calories;
          acc.protein += meal.nutritionalSummary.protein;
          acc.carbs += meal.nutritionalSummary.carbs;
          acc.fat += meal.nutritionalSummary.fat;
          acc.fiber += meal.nutritionalSummary.fiber;
          acc.sugar += meal.nutritionalSummary.sugar;
          acc.sodium += meal.nutritionalSummary.sodium;
          return acc;
        },
        {
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0,
          sugar: 0,
          sodium: 0,
        }
      );

      // Calculate meal distribution
      const mealCounts = data.foodAnalyses.reduce(
        (acc, meal) => {
          const category = meal.mealCategory.toLowerCase();
          if (category === 'breakfast') acc.breakfast++;
          else if (category === 'lunch') acc.lunch++;
          else if (category === 'dinner') acc.dinner++;
          else acc.snacks++;
          return acc;
        },
        { breakfast: 0, lunch: 0, dinner: 0, snacks: 0 }
      );

      setSummaryData(totals);
      setMealDistribution(mealCounts);
    }
  }, [data]);

  // Get nutritional goals from user preferences or set defaults
  const nutritionGoals = user?.preferences.nutritionGoals || {
    calories: 2000,
    protein: 120,
    carbs: 250,
    fat: 70,
  };

  // Calculate nutrition goal multipliers
  const getGoalMultiplier = () => {
    if (timeRange === "weekly") return 7;
    if (timeRange === "monthly") return 30;
    return 1;
  };

  const goalMultiplier = getGoalMultiplier();

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen bg-gray-50">
        <div className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-nutrisnap-charcoal">
                  Nutrition Summary
                </h1>
                <p className="text-gray-500 mt-1">
                  Loading your nutritional data...
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="h-32 bg-gray-200 rounded-lg animate-pulse" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex min-h-screen bg-gray-50">
        <div className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Failed to load nutrition data
              </h3>
              <p className="text-gray-500">
                Please try again later or contact support if the problem persists.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-nutrisnap-charcoal">
                Nutrition Summary
              </h1>
              <p className="text-gray-500 mt-1">
                Detailed breakdown of your nutritional intake
              </p>
            </div>
            <div className="w-48">
              <Select
                value={timeRange}
                onValueChange={(value) => setTimeRange(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <Card>
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-nutrisnap-teal">
                      {data?.foodAnalyses?.length || 0}
                    </p>
                    <p className="text-sm text-gray-500">
                      Meals {timeRange === "daily" ? "today" : `this ${timeRange.slice(0, -2)}`}
                    </p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {Math.round((summaryData.calories / (nutritionGoals.calories * goalMultiplier)) * 100)}%
                    </p>
                    <p className="text-sm text-gray-500">Calorie goal progress</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      {timeRange === "daily" ? "Today" : timeRange === "weekly" ? "7 Days" : "30 Days"}
                    </p>
                    <p className="text-sm text-gray-500">Time period</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Macronutrients */}
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4">Macronutrients</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <NutritionCard
                data={{
                  label: "Calories",
                  value: summaryData.calories,
                  unit: "kcal",
                  goal: nutritionGoals.calories * goalMultiplier,
                  color: "blue",
                }}
              />
              <NutritionCard
                data={{
                  label: "Protein",
                  value: summaryData.protein,
                  unit: "g",
                  goal: nutritionGoals.protein * goalMultiplier,
                  color: "red",
                }}
              />
              <NutritionCard
                data={{
                  label: "Carbs",
                  value: summaryData.carbs,
                  unit: "g",
                  goal: nutritionGoals.carbs * goalMultiplier,
                  color: "green",
                }}
              />
              <NutritionCard
                data={{
                  label: "Fat",
                  value: summaryData.fat,
                  unit: "g",
                  goal: nutritionGoals.fat * goalMultiplier,
                  color: "yellow",
                }}
              />
            </div>
          </div>

          {/* Micronutrients */}
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4">Micronutrients</h2>
            <Card>
              <CardHeader>
                <CardTitle>Details</CardTitle>
                <CardDescription>
                  Breakdown of important micronutrients in your diet
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Fiber</span>
                      <span className="text-sm text-gray-500">
                        {Math.round(summaryData.fiber)}g / {25 * goalMultiplier}g
                      </span>
                    </div>
                    <Progress
                      value={Math.min((summaryData.fiber / (25 * goalMultiplier)) * 100, 100)}
                      className="h-3"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Sugar</span>
                      <span className="text-sm text-gray-500">
                        {Math.round(summaryData.sugar)}g / {30 * goalMultiplier}g
                      </span>
                    </div>
                    <Progress
                      value={Math.min((summaryData.sugar / (30 * goalMultiplier)) * 100, 100)}
                      className="h-3"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Sodium</span>
                      <span className="text-sm text-gray-500">
                        {Math.round(summaryData.sodium)}mg / {2300 * goalMultiplier}mg
                      </span>
                    </div>
                    <Progress
                      value={Math.min((summaryData.sodium / (2300 * goalMultiplier)) * 100, 100)}
                      className="h-3"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Nutrition Breakdown */}
          <div>
            <h2 className="text-xl font-bold mb-4">Nutrition Breakdown</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Calorie Sources</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-60">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={[
                            { name: 'Protein', value: summaryData.protein * 4, fill: '#ef4444' },
                            { name: 'Carbs', value: summaryData.carbs * 4, fill: '#22c55e' },
                            { name: 'Fat', value: summaryData.fat * 9, fill: '#eab308' }
                          ]}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={80}
                          paddingAngle={5}
                          dataKey="value"
                        >
                          {[
                            { name: 'Protein', value: summaryData.protein * 4, fill: '#ef4444' },
                            { name: 'Carbs', value: summaryData.carbs * 4, fill: '#22c55e' },
                            { name: 'Fat', value: summaryData.fat * 9, fill: '#eab308' }
                          ].map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.fill} />
                          ))}
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                    <div className="text-center mt-2">
                      <span className="text-2xl font-bold">{Math.round(summaryData.calories)}</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 mt-4">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-red-500 rounded mr-2"></div>
                      <div>
                        <p className="text-sm font-medium">Protein</p>
                        <p className="text-xs text-gray-500">
                          {summaryData.calories > 0 ? Math.round((summaryData.protein * 4 * 100) / summaryData.calories) : 0}%
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
                      <div>
                        <p className="text-sm font-medium">Carbs</p>
                        <p className="text-xs text-gray-500">
                          {summaryData.calories > 0 ? Math.round((summaryData.carbs * 4 * 100) / summaryData.calories) : 0}%
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
                      <div>
                        <p className="text-sm font-medium">Fat</p>
                        <p className="text-xs text-gray-500">
                          {summaryData.calories > 0 ? Math.round((summaryData.fat * 9 * 100) / summaryData.calories) : 0}%
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Meal Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-60">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={[
                          { name: 'Breakfast', value: mealDistribution.breakfast, fill: '#eab308' },
                          { name: 'Lunch', value: mealDistribution.lunch, fill: '#22c55e' },
                          { name: 'Dinner', value: mealDistribution.dinner, fill: '#3b82f6' },
                          { name: 'Snacks', value: mealDistribution.snacks, fill: '#a855f7' }
                        ]}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Bar dataKey="value" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="grid grid-cols-4 gap-2 mt-4 text-center">
                    <div>
                      <p className="text-xs">Breakfast</p>
                      <p className="text-sm font-semibold">{mealDistribution.breakfast}</p>
                    </div>
                    <div>
                      <p className="text-xs">Lunch</p>
                      <p className="text-sm font-semibold">{mealDistribution.lunch}</p>
                    </div>
                    <div>
                      <p className="text-xs">Dinner</p>
                      <p className="text-sm font-semibold">{mealDistribution.dinner}</p>
                    </div>
                    <div>
                      <p className="text-xs">Snacks</p>
                      <p className="text-sm font-semibold">{mealDistribution.snacks}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NutritionSummary;
