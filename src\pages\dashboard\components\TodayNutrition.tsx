
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import NutritionCard from "@/components/nutrition/NutritionCard";

type TodayNutritionProps = {
  todayNutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  nutritionGoals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
};

const TodayNutrition: React.FC<TodayNutritionProps> = ({
  todayNutrition,
  nutritionGoals,
}) => {
  const navigate = useNavigate();

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-nutrisnap-charcoal">
          Today's Nutrition
        </h2>
        <Button
          variant="ghost"
          className="text-nutrisnap-teal"
          onClick={() => navigate("/nutrition-summary")}
        >
          View Full Summary
          <ChevronRight className="ml-1 h-4 w-4" />
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <NutritionCard
          data={{
            label: "Calories",
            value: todayNutrition.calories,
            unit: "kcal",
            goal: nutritionGoals.calories,
            color: "blue",
          }}
        />
        <NutritionCard
          data={{
            label: "Protein",
            value: todayNutrition.protein,
            unit: "g",
            goal: nutritionGoals.protein,
            color: "red",
          }}
        />
        <NutritionCard
          data={{
            label: "Carbs",
            value: todayNutrition.carbs,
            unit: "g",
            goal: nutritionGoals.carbs,
            color: "green",
          }}
        />
        <NutritionCard
          data={{
            label: "Fat",
            value: todayNutrition.fat,
            unit: "g",
            goal: nutritionGoals.fat,
            color: "yellow",
          }}
        />
      </div>
    </div>
  );
};

export default TodayNutrition;
