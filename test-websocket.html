<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test - Multiple Users</title>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .online-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #000;
            color: white;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            margin: 10px;
        }
        .dot {
            width: 8px;
            height: 8px;
            background: #00ff00;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .controls {
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .connections {
            margin: 20px 0;
        }
        .connection-item {
            padding: 5px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Live WebSocket Test - Online Users Counter</h1>
        
        <div class="online-button">
            <div class="dot"></div>
            <span id="onlineCount">0</span>
            <span style="color: #ccc;">online</span>
            <span>💬</span>
        </div>

        <div class="controls">
            <button onclick="connectUser()">➕ Connect New User</button>
            <button onclick="disconnectRandomUser()">➖ Disconnect Random User</button>
            <button onclick="disconnectAllUsers()">🔌 Disconnect All</button>
        </div>

        <div class="status">
            <strong>Status:</strong> <span id="status">Disconnected</span><br>
            <strong>Active Connections:</strong> <span id="connectionCount">0</span>
        </div>

        <div class="connections">
            <h3>Active Connections:</h3>
            <div id="connectionsList"></div>
        </div>
    </div>

    <script>
        let connections = [];
        let onlineCount = 0;

        function updateUI() {
            document.getElementById('onlineCount').textContent = onlineCount;
            document.getElementById('connectionCount').textContent = connections.length;
            
            const list = document.getElementById('connectionsList');
            list.innerHTML = connections.map((conn, index) => 
                `<div class="connection-item">
                    Connection ${index + 1}: ${conn.id} 
                    ${conn.connected ? '🟢 Connected' : '🔴 Disconnected'}
                </div>`
            ).join('');
        }

        function connectUser() {
            const socket = io('http://localhost:5001');
            
            socket.on('connect', () => {
                console.log('New user connected:', socket.id);
                document.getElementById('status').textContent = 'Connected';
                connections.push({ socket, id: socket.id, connected: true });
                updateUI();
            });

            socket.on('onlineCount', (count) => {
                console.log('Online count updated:', count);
                onlineCount = count;
                updateUI();
            });

            socket.on('disconnect', () => {
                console.log('User disconnected:', socket.id);
                const conn = connections.find(c => c.id === socket.id);
                if (conn) {
                    conn.connected = false;
                }
                updateUI();
            });
        }

        function disconnectRandomUser() {
            const connectedUsers = connections.filter(c => c.connected);
            if (connectedUsers.length > 0) {
                const randomUser = connectedUsers[Math.floor(Math.random() * connectedUsers.length)];
                randomUser.socket.disconnect();
                randomUser.connected = false;
                updateUI();
            }
        }

        function disconnectAllUsers() {
            connections.forEach(conn => {
                if (conn.connected) {
                    conn.socket.disconnect();
                    conn.connected = false;
                }
            });
            updateUI();
        }

        // Auto-connect one user on page load
        setTimeout(connectUser, 1000);
    </script>
</body>
</html>
