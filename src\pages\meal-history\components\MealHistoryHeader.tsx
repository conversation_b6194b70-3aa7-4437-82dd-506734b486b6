
import React from "react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Camera, TrendingUp, Plus } from "lucide-react";

const MealHistoryHeader = () => {
  const navigate = useNavigate();

  return (
    <div className="mb-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-nutrisnap-charcoal">
            Meal History
          </h1>
          <p className="text-gray-500 mt-1">
            Browse and search your previous meals
          </p>
        </div>
        <div className="flex gap-3 mt-4 md:mt-0">
          <Button
            variant="outline"
            onClick={() => navigate("/nutrition-summary")}
            className="flex items-center gap-2"
          >
            <TrendingUp className="h-4 w-4" />
            View Trends
          </Button>
          <Button
            onClick={() => navigate("/snap-new")}
            className="bg-nutrisnap-teal hover:bg-nutrisnap-teal/90 text-white flex items-center gap-2"
          >
            <Camera className="h-4 w-4" />
            Snap New Meal
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MealHistoryHeader;
