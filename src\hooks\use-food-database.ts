import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import FoodDatabaseService from '@/services/food-database.service';
import { toast } from 'sonner';

export const useFoodSearch = (params?: {
  search?: string;
  category?: string;
  limit?: number;
  page?: number;
}) => {
  return useQuery({
    queryKey: ['foodSearch', params],
    queryFn: () => FoodDatabaseService.searchFoods(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
    keepPreviousData: true,
  });
};

export const useFoodCategories = () => {
  return useQuery({
    queryKey: ['foodCategories'],
    queryFn: () => FoodDatabaseService.getCategories(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

export const useFoodItem = (id: string) => {
  return useQuery({
    queryKey: ['foodItem', id],
    queryFn: () => FoodDatabaseService.getFoodById(id),
    staleTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!id,
  });
};

// Admin only mutations
export const useCreateFoodItem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => FoodDatabaseService.createFood(data),
    onSuccess: () => {
      // Invalidate food search query to refetch
      queryClient.invalidateQueries({ queryKey: ['foodSearch'] });
      // Invalidate food categories in case a new category was added
      queryClient.invalidateQueries({ queryKey: ['foodCategories'] });
      
      toast.success('Food item created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create food item');
    }
  });
};

export const useUpdateFoodItem = (id: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => FoodDatabaseService.updateFood(id, data),
    onSuccess: () => {
      // Invalidate specific food item query to refetch
      queryClient.invalidateQueries({ queryKey: ['foodItem', id] });
      // Invalidate food search query to refetch
      queryClient.invalidateQueries({ queryKey: ['foodSearch'] });
      // Invalidate food categories in case the category was changed
      queryClient.invalidateQueries({ queryKey: ['foodCategories'] });
      
      toast.success('Food item updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update food item');
    }
  });
};

export const useDeleteFoodItem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => FoodDatabaseService.deleteFood(id),
    onSuccess: () => {
      // Invalidate food search query to refetch
      queryClient.invalidateQueries({ queryKey: ['foodSearch'] });
      
      toast.success('Food item deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete food item');
    }
  });
};
