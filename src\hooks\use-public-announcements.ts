import { useQuery } from '@tanstack/react-query';
import AnnouncementsService, { PublicAnnouncement } from '@/services/announcements.service';

// Hook for fetching active public announcements
export const usePublicAnnouncements = (params?: {
  page?: number;
  limit?: number;
  category?: string;
  importance?: string;
}) => {
  return useQuery({
    queryKey: ['public-announcements', params],
    queryFn: () => AnnouncementsService.getActiveAnnouncements(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for fetching recent announcements (for homepage/widgets)
export const useRecentAnnouncements = (limit: number = 5) => {
  return useQuery({
    queryKey: ['recent-announcements', limit],
    queryFn: () => AnnouncementsService.getActiveAnnouncements({ limit }),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};
