
import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import SettingsLayout from "./SettingsLayout";
import ProfileSection from "./sections/ProfileSection";
import PasswordSection from "./sections/PasswordSection";
import DietaryPreferencesSection from "./sections/DietaryPreferencesSection";
import NotificationsSection from "./sections/NotificationsSection";
import AccountActionsSection from "./sections/AccountActionsSection";
import { toast } from "sonner";

const Settings = () => {
  const { user } = useAuth();
  const [loading, setLoading] = React.useState(false);
  
  const [formData, setFormData] = React.useState({
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    email: user?.email || "",
    password: "",
    confirmPassword: "",
    dietaryRestrictions: user?.preferences.dietaryRestrictions || [],
    nutritionGoals: {
      calories: user?.preferences.nutritionGoals.calories || 2000,
      protein: user?.preferences.nutritionGoals.protein || 120,
      carbs: user?.preferences.nutritionGoals.carbs || 250,
      fat: user?.preferences.nutritionGoals.fat || 70,
    },
    units: user?.preferences.units || "metric",
    notifications: {
      mealReminders: true,
      weeklyReports: true,
      achievementAlerts: true,
    },
  });

  const handleSaveSettings = (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      toast.success("Settings saved successfully");
      setLoading(false);
    }, 1000);
  };

  return (
    <SettingsLayout>
      <form onSubmit={handleSaveSettings}>
        <ProfileSection formData={formData} setFormData={setFormData} />
        <PasswordSection formData={formData} setFormData={setFormData} />
        <DietaryPreferencesSection formData={formData} setFormData={setFormData} />
        <NotificationsSection formData={formData} setFormData={setFormData} />
        <AccountActionsSection loading={loading} />
      </form>
    </SettingsLayout>
  );
};

export default Settings;
