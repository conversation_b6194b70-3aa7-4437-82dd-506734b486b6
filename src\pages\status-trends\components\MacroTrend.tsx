
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";

interface MacroTrendProps {
  trendData: Array<{
    date: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    analysisCount: number;
  }>;
}

const MacroTrend: React.FC<MacroTrendProps> = ({ trendData }) => {
  return (
    <Card className="shadow-md overflow-hidden border-none bg-white hover:shadow-lg transition-shadow duration-300">
      <CardHeader className="bg-gradient-to-r from-nutrisnap-purple/10 to-transparent pb-4">
        <CardTitle className="text-lg font-medium text-nutrisnap-charcoal flex items-center gap-2">
          <span className="inline-block w-3 h-3 bg-nutrisnap-purple rounded-full"></span>
          Macronutrient Trends
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="h-64 relative">
          <div className="absolute inset-0 flex items-end">
            {trendData.slice(-14).map((day, i) => (
              <div 
                key={i} 
                className="flex-1 flex flex-col items-center justify-end h-full"
              >
                <div className="relative w-8 hover:scale-110 transition-transform duration-200">
                  <div 
                    className="absolute bottom-0 w-2 left-0 bg-green-400 rounded-t-md" 
                    style={{ 
                      height: `${Math.min(day.carbs, 250) / 2.5}%`,
                      opacity: day.analysisCount === 0 ? 0.2 : 1
                    }}
                  ></div>
                  <div 
                    className="absolute bottom-0 w-2 left-3 bg-red-400 rounded-t-md" 
                    style={{ 
                      height: `${Math.min(day.protein, 120) / 1.2}%`,
                      opacity: day.analysisCount === 0 ? 0.2 : 1
                    }}
                  ></div>
                  <div 
                    className="absolute bottom-0 w-2 left-6 bg-amber-400 rounded-t-md" 
                    style={{ 
                      height: `${Math.min(day.fat, 70) / 0.7}%`,
                      opacity: day.analysisCount === 0 ? 0.2 : 1
                    }}
                  ></div>
                </div>
                <div className="text-xs mt-1 text-gray-500">
                  {new Date(day.date).getDate()}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="flex justify-center space-x-6 mt-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
            <span className="text-xs font-medium">Carbs</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
            <span className="text-xs font-medium">Protein</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-amber-400 rounded-full mr-2"></div>
            <span className="text-xs font-medium">Fat</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MacroTrend;
