import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { HealthInsightData } from '../StatusTrends';
import { 
  Lightbulb, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  Heart,
  Brain,
  Shield,
  Zap,
  ChevronRight,
  X
} from 'lucide-react';

interface HealthInsightsProps {
  data: HealthInsightData[];
}

const HealthInsights: React.FC<HealthInsightsProps> = ({ data }) => {
  const [dismissedInsights, setDismissedInsights] = useState<string[]>([]);

  // Insight type configuration
  const insightConfig = {
    tip: {
      icon: Lightbulb,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      badgeColor: 'bg-blue-100 text-blue-800',
    },
    warning: {
      icon: Alert<PERSON>rian<PERSON>,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      badgeColor: 'bg-orange-100 text-orange-800',
    },
    achievement: {
      icon: CheckCircle,
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      badgeColor: 'bg-green-100 text-green-800',
    },
    recommendation: {
      icon: TrendingUp,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      badgeColor: 'bg-purple-100 text-purple-800',
    },
  };

  // Priority configuration
  const priorityConfig = {
    high: {
      label: 'High Priority',
      color: 'bg-red-100 text-red-800',
      order: 1,
    },
    medium: {
      label: 'Medium Priority',
      color: 'bg-yellow-100 text-yellow-800',
      order: 2,
    },
    low: {
      label: 'Low Priority',
      color: 'bg-gray-100 text-gray-800',
      order: 3,
    },
  };

  // Filter out dismissed insights and sort by priority
  const visibleInsights = data
    .filter(insight => !dismissedInsights.includes(insight.id))
    .sort((a, b) => priorityConfig[a.priority].order - priorityConfig[b.priority].order);

  // Dismiss insight
  const dismissInsight = (insightId: string) => {
    setDismissedInsights(prev => [...prev, insightId]);
  };

  // Get insight icon based on type
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'nutrition': return Heart;
      case 'habit': return Brain;
      case 'health': return Shield;
      case 'performance': return Zap;
      default: return Lightbulb;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Health Insights
        </CardTitle>
        
        {/* Insights Summary */}
        <div className="flex items-center gap-4 mt-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">Total Insights</p>
            <p className="text-lg font-semibold">{visibleInsights.length}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">High Priority</p>
            <p className="text-lg font-semibold text-red-600">
              {visibleInsights.filter(i => i.priority === 'high').length}
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {visibleInsights.length > 0 ? (
          visibleInsights.map((insight) => {
            const config = insightConfig[insight.type];
            const IconComponent = config.icon;
            const priorityInfo = priorityConfig[insight.priority];
            
            return (
              <div
                key={insight.id}
                className={`p-4 rounded-lg border ${config.borderColor} ${config.bgColor} relative`}
              >
                {/* Dismiss Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-white/50"
                  onClick={() => dismissInsight(insight.id)}
                >
                  <X className="h-3 w-3" />
                </Button>

                <div className="flex items-start gap-3 pr-8">
                  {/* Icon */}
                  <div className="p-2 rounded-lg bg-white border">
                    <IconComponent className={`h-5 w-5 ${config.color}`} />
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    {/* Header */}
                    <div className="flex items-center gap-2 mb-2">
                      <h5 className="font-medium text-gray-900">
                        {insight.title}
                      </h5>
                      <Badge className={`text-xs ${config.badgeColor}`}>
                        {insight.type}
                      </Badge>
                      {insight.priority === 'high' && (
                        <Badge className={`text-xs ${priorityInfo.color}`}>
                          {priorityInfo.label}
                        </Badge>
                      )}
                    </div>

                    {/* Message */}
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {insight.message}
                    </p>

                    {/* Action Button for recommendations */}
                    {insight.type === 'recommendation' && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-3 text-xs"
                      >
                        Learn More
                        <ChevronRight className="h-3 w-3 ml-1" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div className="text-center py-8">
            <Brain className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">
              No Insights Available
            </h4>
            <p className="text-sm text-gray-500">
              Keep logging meals to get personalized health insights!
            </p>
          </div>
        )}

        {/* Insight Categories */}
        {visibleInsights.length > 0 && (
          <div className="mt-6">
            <h4 className="text-sm font-medium mb-3">Insight Categories</h4>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(insightConfig).map(([type, config]) => {
                const typeInsights = visibleInsights.filter(i => i.type === type);
                const IconComponent = config.icon;
                
                return (
                  <div key={type} className="flex items-center gap-2 p-2 rounded-lg bg-gray-50">
                    <IconComponent className={`h-4 w-4 ${config.color}`} />
                    <div>
                      <p className="text-xs font-medium capitalize">{type}</p>
                      <p className="text-xs text-gray-600">
                        {typeInsights.length} insight{typeInsights.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* AI-Powered Message */}
        <div className="mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Brain className="h-4 w-4 text-indigo-600" />
            <span className="font-medium text-indigo-800">AI-Powered Insights</span>
          </div>
          <p className="text-sm text-indigo-700">
            These insights are generated based on your nutrition patterns, goals, and health data. 
            They're designed to help you make informed decisions about your nutrition journey.
          </p>
        </div>

        {/* Quick Actions */}
        {visibleInsights.some(i => i.type === 'recommendation') && (
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-3">Quick Actions</h4>
            <div className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Heart className="h-4 w-4 mr-2" />
                View Nutrition Goals
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <TrendingUp className="h-4 w-4 mr-2" />
                Track Progress
              </Button>
            </div>
          </div>
        )}

        {/* Refresh Insights */}
        <div className="mt-6 text-center">
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-600 hover:text-gray-800"
            onClick={() => setDismissedInsights([])}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Refresh Insights
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default HealthInsights;
