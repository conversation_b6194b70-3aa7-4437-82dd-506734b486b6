
import React, { useState, useEffect } from "react";
import { useFoodAnalyses } from "@/hooks/use-food-analysis";
import MealHistoryHeader from "./components/MealHistoryHeader";
import SearchFilters from "./components/SearchFilters";
import MealGrid from "./components/MealGrid";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

const MealHistory = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    startDate: "",
    endDate: "",
  });

  // Fetch food analyses with API filters
  const {
    data,
    isLoading,
    error,
    refetch
  } = useFoodAnalyses({
    ...(selectedCategory && { mealCategory: selectedCategory }),
    ...(dateRange.startDate && { startDate: dateRange.startDate }),
    ...(dateRange.endDate && { endDate: dateRange.endDate })
  });

  // Extract meals from the API response
  const meals = data?.foodAnalyses || [];

  // Refetch data when filters change
  useEffect(() => {
    refetch();
  }, [selectedCategory, dateRange.startDate, dateRange.endDate, refetch]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value === "all" ? null : value);
  };

  const handleDateChange = (field: "startDate" | "endDate", value: string) => {
    setDateRange((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Client-side filtering for search term (since the API doesn't support text search)
  const filteredMeals = meals.filter((meal) => {
    // Filter by search term
    const termMatch = searchTerm
      ? meal.userNotes.toLowerCase().includes(searchTerm.toLowerCase()) ||
        meal.recognitionResults.some((item) =>
          item.foodItem.toLowerCase().includes(searchTerm.toLowerCase())
        )
      : true;

    return termMatch;
  });

  // Handle error state
  if (error) {
    return (
      <div className="flex min-h-screen bg-gray-50">
        <div className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">
            <MealHistoryHeader />

            <SearchFilters
              searchTerm={searchTerm}
              selectedCategory={selectedCategory}
              dateRange={dateRange}
              handleSearch={handleSearch}
              handleCategoryChange={handleCategoryChange}
              handleDateChange={handleDateChange}
            />

            <Alert variant="destructive" className="my-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                Failed to load your meal history. Please try again later.
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          <MealHistoryHeader />

          <SearchFilters
            searchTerm={searchTerm}
            selectedCategory={selectedCategory}
            dateRange={dateRange}
            handleSearch={handleSearch}
            handleCategoryChange={handleCategoryChange}
            handleDateChange={handleDateChange}
          />

          {isLoading ? (
            <div className="space-y-4 my-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <Skeleton key={index} className="h-64 rounded-lg" />
                ))}
              </div>
            </div>
          ) : filteredMeals.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-gray-900 mb-2">No meals found</h3>
              <p className="text-gray-500">
                Try adjusting your filters or add some meals to see them here.
              </p>
            </div>
          ) : (
            <MealGrid
              meals={filteredMeals}
              searchTerm={searchTerm}
              selectedCategory={selectedCategory}
              dateRange={dateRange}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default MealHistory;
