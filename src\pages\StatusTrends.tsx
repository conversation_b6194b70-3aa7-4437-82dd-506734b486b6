import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, TrendingUp, Target, Calendar, Award } from 'lucide-react';
import { formatDisplayNumber, formatCalories } from '@/utils/numberUtils';

// Import trend components
import TrendChart from './status-trends/TrendChart';
import GoalProgress from './status-trends/GoalProgress';
import MealPatterns from './status-trends/MealPatterns';
import Achievements from './status-trends/Achievements';
import HealthInsights from './status-trends/HealthInsights';

// Types for status trends data
export interface TrendData {
  date: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  sugar: number;
  sodium: number;
}

export interface GoalData {
  calories: { current: number; target: number; percentage: number };
  protein: { current: number; target: number; percentage: number };
  carbs: { current: number; target: number; percentage: number };
  fat: { current: number; target: number; percentage: number };
}

export interface MealPatternData {
  category: string;
  count: number;
  percentage: number;
  avgCalories: number;
}

export interface AchievementData {
  id: string;
  title: string;
  description: string;
  type: 'streak' | 'goal' | 'milestone';
  earned: boolean;
  earnedDate?: string;
  progress?: number;
  target?: number;
}

export interface HealthInsightData {
  id: string;
  type: 'tip' | 'warning' | 'achievement' | 'recommendation';
  title: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
}

export interface StatusTrendsData {
  period: number;
  trends: TrendData[];
  goals: GoalData;
  mealPatterns: MealPatternData[];
  achievements: AchievementData[];
  insights: HealthInsightData[];
  summary: {
    totalMeals: number;
    avgCaloriesPerDay: number;
    streakDays: number;
    goalsMetPercentage: number;
  };
}

const StatusTrends: React.FC = () => {
  const { user } = useAuth();
  const [period, setPeriod] = useState<number>(30);
  const [data, setData] = useState<StatusTrendsData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch status trends data
  const fetchStatusTrends = async (selectedPeriod: number) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/status-trends?period=${selectedPeriod}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('nutrisnap_token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch status trends: ${response.statusText}`);
      }

      const result = await response.json();
      setData(result.data);
    } catch (err) {
      console.error('Error fetching status trends:', err);
      setError(err instanceof Error ? err.message : 'Failed to load status trends');
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount and period change
  useEffect(() => {
    fetchStatusTrends(period);
  }, [period]);

  // Period selection options
  const periodOptions = [
    { value: 7, label: '7 Days' },
    { value: 30, label: '30 Days' },
    { value: 90, label: '90 Days' },
  ];

  // Loading state
  if (loading) {
    return (
      <div className="flex min-h-screen bg-gray-50">
        <div className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">
            <div className="mb-8">
              <Skeleton className="h-10 w-64 mb-4" />
              <div className="flex gap-2">
                {periodOptions.map((_, index) => (
                  <Skeleton key={index} className="h-10 w-20" />
                ))}
              </div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
              <div className="lg:col-span-2">
                <Skeleton className="h-80 rounded-lg" />
              </div>
              <div>
                <Skeleton className="h-80 rounded-lg" />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Skeleton className="h-64 rounded-lg" />
              <Skeleton className="h-64 rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !data) {
    return (
      <div className="flex min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto w-full">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-nutrisnap-charcoal mb-2">
              Status & Trends
            </h1>
            <p className="text-gray-600">
              Track your nutrition progress and discover insights
            </p>
          </div>

          <Alert variant="destructive" className="my-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || 'Failed to load status trends data. Please try again later.'}
            </AlertDescription>
          </Alert>

          <Button onClick={() => fetchStatusTrends(period)} className="mt-4">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-nutrisnap-charcoal mb-2">
              Status & Trends
            </h1>
            <p className="text-gray-600 mb-6">
              Track your nutrition progress and discover insights
            </p>

            {/* Period Selection */}
            <div className="flex gap-2">
              {periodOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={period === option.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPeriod(option.value)}
                  className={period === option.value ? 'bg-nutrisnap-teal' : ''}
                >
                  <Calendar className="h-4 w-4 mr-1" />
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Meals</p>
                    <p className="text-2xl font-bold">{data.summary.totalMeals}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-nutrisnap-teal" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Avg Calories/Day</p>
                    <p className="text-2xl font-bold">
                      {formatCalories(data.summary.avgCaloriesPerDay)}
                    </p>
                  </div>
                  <Target className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Current Streak</p>
                    <p className="text-2xl font-bold">{data.summary.streakDays} days</p>
                  </div>
                  <Award className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Goals Met</p>
                    <p className="text-2xl font-bold">
                      {formatDisplayNumber(data.summary.goalsMetPercentage)}%
                    </p>
                  </div>
                  <Target className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Trend Chart - Takes 2/3 width */}
            <div className="lg:col-span-2">
              <TrendChart data={data.trends} period={period} />
            </div>

            {/* Goal Progress - Takes 1/3 width */}
            <div>
              <GoalProgress data={data.goals} />
            </div>
          </div>

          {/* Bottom Row */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Meal Patterns */}
            <div>
              <MealPatterns data={data.mealPatterns} />
            </div>

            {/* Achievements */}
            <div>
              <Achievements data={data.achievements} />
            </div>

            {/* Health Insights */}
            <div>
              <HealthInsights data={data.insights} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatusTrends;
