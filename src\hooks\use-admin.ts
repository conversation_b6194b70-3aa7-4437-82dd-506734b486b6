import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import AdminService, {
  DashboardStats,
  AdminUser,
  CreateUser,
  UpdateUser,
  GalleryItem,
  CreateGalleryItem,
  UpdateGalleryItem,
  Announcement,
  CreateAnnouncement,
  UpdateAnnouncement,
  SystemConfig,
  CreateSystemConfig,
  SystemLog,
  LogStats,
} from '@/services/admin.service';
import { toast } from 'sonner';

// Dashboard & Analytics Hooks
export const useAdminDashboard = () => {
  return useQuery({
    queryKey: ['admin', 'dashboard'],
    queryFn: AdminService.getDashboard,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useAdminAnalytics = () => {
  return useQuery({
    queryKey: ['admin', 'analytics'],
    queryFn: AdminService.getAnalytics,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useSystemInfo = () => {
  return useQuery({
    queryKey: ['admin', 'system-info'],
    queryFn: AdminService.getSystemInfo,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

// User Management Hooks
export const useAdminUsers = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
}) => {
  return useQuery({
    queryKey: ['admin', 'users', params],
    queryFn: () => AdminService.getUsers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useAdminUser = (id: string) => {
  return useQuery({
    queryKey: ['admin', 'users', id],
    queryFn: () => AdminService.getUserById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userData: CreateUser) => AdminService.createUser(userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
      toast.success('User created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create user');
    },
  });
};

export const useUpdateUser = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userData: UpdateUser) => AdminService.updateUser(id, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'users', id] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
      toast.success('User updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update user');
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => AdminService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
      toast.success('User deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete user');
    },
  });
};

// Gallery Management Hooks
export const useAdminGallery = (params?: {
  page?: number;
  limit?: number;
  category?: string;
  featured?: boolean;
  search?: string;
}) => {
  return useQuery({
    queryKey: ['admin', 'gallery', params],
    queryFn: () => AdminService.getGalleryItems(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useAdminGalleryItem = (id: string) => {
  return useQuery({
    queryKey: ['admin', 'gallery', id],
    queryFn: () => AdminService.getGalleryItem(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateGalleryItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateGalleryItem) => AdminService.createGalleryItem(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'gallery'] });
      queryClient.invalidateQueries({ queryKey: ['gallery'] }); // Public gallery
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
      toast.success('Gallery item created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create gallery item');
    },
  });
};

export const useUpdateGalleryItem = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateGalleryItem) => AdminService.updateGalleryItem(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'gallery'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'gallery', id] });
      queryClient.invalidateQueries({ queryKey: ['gallery'] }); // Public gallery
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
      toast.success('Gallery item updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update gallery item');
    },
  });
};

export const useDeleteGalleryItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => AdminService.deleteGalleryItem(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'gallery'] });
      queryClient.invalidateQueries({ queryKey: ['gallery'] }); // Public gallery
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
      toast.success('Gallery item deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete gallery item');
    },
  });
};

// Announcements Management Hooks
export const useAdminAnnouncements = (params?: {
  page?: number;
  limit?: number;
  active?: boolean;
  type?: string;
  priority?: string;
}) => {
  return useQuery({
    queryKey: ['admin', 'announcements', params],
    queryFn: () => AdminService.getAllAnnouncements(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useAdminAnnouncement = (id: string) => {
  return useQuery({
    queryKey: ['admin', 'announcements', id],
    queryFn: () => AdminService.getAnnouncement(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateAnnouncement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAnnouncement) => AdminService.createAnnouncement(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'announcements'] });
      queryClient.invalidateQueries({ queryKey: ['announcements'] }); // Public announcements
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
      toast.success('Announcement created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create announcement');
    },
  });
};

export const useUpdateAnnouncement = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateAnnouncement) => AdminService.updateAnnouncement(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'announcements'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'announcements', id] });
      queryClient.invalidateQueries({ queryKey: ['announcements'] }); // Public announcements
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
      toast.success('Announcement updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update announcement');
    },
  });
};

export const useDeleteAnnouncement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => AdminService.deleteAnnouncement(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'announcements'] });
      queryClient.invalidateQueries({ queryKey: ['announcements'] }); // Public announcements
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
      toast.success('Announcement deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete announcement');
    },
  });
};

// System Configuration Hooks
export const useSystemConfigurations = (params?: {
  category?: string;
  isPublic?: boolean;
}) => {
  return useQuery({
    queryKey: ['admin', 'config', params],
    queryFn: () => AdminService.getConfigurations(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useSystemConfiguration = (key: string) => {
  return useQuery({
    queryKey: ['admin', 'config', key],
    queryFn: () => AdminService.getConfiguration(key),
    enabled: !!key,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useCreateConfiguration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSystemConfig) => AdminService.createConfiguration(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'config'] });
      toast.success('Configuration created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create configuration');
    },
  });
};

export const useUpdateConfiguration = (key: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<CreateSystemConfig>) => AdminService.updateConfiguration(key, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'config'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'config', key] });
      toast.success('Configuration updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update configuration');
    },
  });
};

export const useDeleteConfiguration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (key: string) => AdminService.deleteConfiguration(key),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'config'] });
      toast.success('Configuration deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete configuration');
    },
  });
};

// System Logs Hooks
export const useSystemLogs = (params?: {
  page?: number;
  limit?: number;
  level?: string;
  category?: string;
  startDate?: string;
  endDate?: string;
  userId?: string;
}) => {
  return useQuery({
    queryKey: ['admin', 'logs', params],
    queryFn: () => AdminService.getLogs(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useLogStats = () => {
  return useQuery({
    queryKey: ['admin', 'logs', 'stats'],
    queryFn: AdminService.getLogStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useRecentLogs = (limit?: number) => {
  return useQuery({
    queryKey: ['admin', 'logs', 'recent', limit],
    queryFn: () => AdminService.getRecentLogs(limit),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useSystemLog = (id: string) => {
  return useQuery({
    queryKey: ['admin', 'logs', id],
    queryFn: () => AdminService.getLog(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useCleanupLogs = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (olderThanDays?: number) => AdminService.cleanupLogs(olderThanDays),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'logs'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'logs', 'stats'] });
      toast.success(`Cleaned up ${result.deletedCount} log entries`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to cleanup logs');
    },
  });
};

export const useExportLogs = () => {
  return useMutation({
    mutationFn: (params?: {
      format?: 'json' | 'csv';
      level?: string;
      category?: string;
      startDate?: string;
      endDate?: string;
    }) => AdminService.exportLogs(params),
    onSuccess: (blob, variables) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `system-logs.${variables?.format || 'json'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Logs exported successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to export logs');
    },
  });
};
