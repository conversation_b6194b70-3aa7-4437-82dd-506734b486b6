
// Mock data for NutriSnap app

// Mock Food Analyses Collection
export const mockFoodAnalyses = [
  {
    id: "fa1",
    userId: "1",
    imageUrl: "https://images.unsplash.com/photo-1482049016688-2d3e1b311543?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80",
    mealCategory: "breakfast",
    mealDateTime: "2023-05-01T08:30:00Z",
    userNotes: "Morning pancakes with blueberries",
    recognitionResults: [
      {
        foodItem: "Pancakes",
        confidence: 0.92,
        boundingBox: { x: 100, y: 100, width: 300, height: 200 },
        quantityGrams: 120,
        commonPortions: ["1 stack (3 pancakes)", "1 pancake"],
        selectedPortion: "1 stack (3 pancakes)",
        userVerified: true,
      },
      {
        foodItem: "Blueberries",
        confidence: 0.89,
        boundingBox: { x: 250, y: 150, width: 100, height: 100 },
        quantityGrams: 50,
        commonPortions: ["1 cup", "1/2 cup", "1/4 cup"],
        selectedPortion: "1/2 cup",
        userVerified: true,
      },
      {
        foodItem: "Maple Syrup",
        confidence: 0.75,
        boundingBox: { x: 200, y: 180, width: 150, height: 50 },
        quantityGrams: 30,
        commonPortions: ["1 tbsp", "2 tbsp"],
        selectedPortion: "2 tbsp",
        userVerified: true,
      },
    ],
    nutritionalSummary: {
      calories: 450,
      protein: 8,
      carbs: 76,
      fat: 12,
      fiber: 4,
      sugar: 38,
      sodium: 390,
    },
  },
  {
    id: "fa2",
    userId: "1",
    imageUrl: "https://images.unsplash.com/photo-1547592180-85f173990888?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80",
    mealCategory: "lunch",
    mealDateTime: "2023-05-01T12:30:00Z",
    userNotes: "Grilled chicken salad",
    recognitionResults: [
      {
        foodItem: "Grilled Chicken Breast",
        confidence: 0.94,
        boundingBox: { x: 150, y: 150, width: 200, height: 150 },
        quantityGrams: 120,
        commonPortions: ["1 breast", "1/2 breast"],
        selectedPortion: "1 breast",
        userVerified: true,
      },
      {
        foodItem: "Mixed Greens",
        confidence: 0.88,
        boundingBox: { x: 100, y: 100, width: 350, height: 250 },
        quantityGrams: 80,
        commonPortions: ["1 cup", "2 cups"],
        selectedPortion: "2 cups",
        userVerified: true,
      },
      {
        foodItem: "Cherry Tomatoes",
        confidence: 0.92,
        boundingBox: { x: 200, y: 200, width: 100, height: 100 },
        quantityGrams: 50,
        commonPortions: ["5 tomatoes", "10 tomatoes"],
        selectedPortion: "10 tomatoes",
        userVerified: true,
      },
      {
        foodItem: "Balsamic Vinaigrette",
        confidence: 0.85,
        boundingBox: { x: 300, y: 200, width: 100, height: 50 },
        quantityGrams: 15,
        commonPortions: ["1 tbsp", "2 tbsp"],
        selectedPortion: "1 tbsp",
        userVerified: true,
      },
    ],
    nutritionalSummary: {
      calories: 320,
      protein: 35,
      carbs: 12,
      fat: 15,
      fiber: 4,
      sugar: 5,
      sodium: 320,
    },
  },
  {
    id: "fa3",
    userId: "1",
    imageUrl: "https://images.unsplash.com/photo-1565299507177-b0ac66763828?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80",
    mealCategory: "dinner",
    mealDateTime: "2023-05-01T19:00:00Z",
    userNotes: "Salmon with asparagus and quinoa",
    recognitionResults: [
      {
        foodItem: "Grilled Salmon",
        confidence: 0.95,
        boundingBox: { x: 150, y: 150, width: 200, height: 150 },
        quantityGrams: 150,
        commonPortions: ["4 oz fillet", "6 oz fillet"],
        selectedPortion: "6 oz fillet",
        userVerified: true,
      },
      {
        foodItem: "Asparagus",
        confidence: 0.92,
        boundingBox: { x: 200, y: 200, width: 150, height: 100 },
        quantityGrams: 100,
        commonPortions: ["5 spears", "10 spears"],
        selectedPortion: "10 spears",
        userVerified: true,
      },
      {
        foodItem: "Quinoa",
        confidence: 0.88,
        boundingBox: { x: 100, y: 250, width: 150, height: 100 },
        quantityGrams: 80,
        commonPortions: ["1/2 cup", "1 cup"],
        selectedPortion: "1/2 cup",
        userVerified: true,
      },
      {
        foodItem: "Olive Oil",
        confidence: 0.75,
        boundingBox: { x: 300, y: 200, width: 50, height: 50 },
        quantityGrams: 10,
        commonPortions: ["1 tsp", "1 tbsp"],
        selectedPortion: "1 tbsp",
        userVerified: true,
        userAdded: true,
      },
    ],
    nutritionalSummary: {
      calories: 480,
      protein: 40,
      carbs: 30,
      fat: 22,
      fiber: 7,
      sugar: 2,
      sodium: 125,
    },
  },
  {
    id: "fa4",
    userId: "1",
    imageUrl: "https://images.unsplash.com/photo-1525385133512-2f3bdd039054?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80",
    mealCategory: "snack",
    mealDateTime: "2023-05-02T15:30:00Z",
    userNotes: "Apple with peanut butter",
    recognitionResults: [
      {
        foodItem: "Apple",
        confidence: 0.97,
        boundingBox: { x: 150, y: 150, width: 150, height: 150 },
        quantityGrams: 180,
        commonPortions: ["1 medium apple", "1 large apple"],
        selectedPortion: "1 medium apple",
        userVerified: true,
      },
      {
        foodItem: "Peanut Butter",
        confidence: 0.92,
        boundingBox: { x: 200, y: 250, width: 100, height: 50 },
        quantityGrams: 32,
        commonPortions: ["1 tbsp", "2 tbsp"],
        selectedPortion: "2 tbsp",
        userVerified: true,
      },
    ],
    nutritionalSummary: {
      calories: 290,
      protein: 8,
      carbs: 30,
      fat: 16,
      fiber: 5,
      sugar: 19,
      sodium: 150,
    },
  },
  {
    id: "fa5",
    userId: "1",
    imageUrl: "https://images.unsplash.com/photo-1605851868183-7a4de52117fa?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80",
    mealCategory: "breakfast",
    mealDateTime: "2023-05-02T08:00:00Z",
    userNotes: "Avocado toast with egg",
    recognitionResults: [
      {
        foodItem: "Whole Grain Bread",
        confidence: 0.92,
        boundingBox: { x: 100, y: 150, width: 250, height: 100 },
        quantityGrams: 60,
        commonPortions: ["1 slice", "2 slices"],
        selectedPortion: "2 slices",
        userVerified: true,
      },
      {
        foodItem: "Avocado",
        confidence: 0.95,
        boundingBox: { x: 150, y: 175, width: 150, height: 75 },
        quantityGrams: 50,
        commonPortions: ["1/4 avocado", "1/2 avocado"],
        selectedPortion: "1/2 avocado",
        userVerified: true,
      },
      {
        foodItem: "Fried Egg",
        confidence: 0.94,
        boundingBox: { x: 200, y: 125, width: 150, height: 150 },
        quantityGrams: 50,
        commonPortions: ["1 egg", "2 eggs"],
        selectedPortion: "1 egg",
        userVerified: true,
      },
      {
        foodItem: "Cherry Tomatoes",
        confidence: 0.88,
        boundingBox: { x: 300, y: 200, width: 75, height: 75 },
        quantityGrams: 30,
        commonPortions: ["3 tomatoes", "5 tomatoes"],
        selectedPortion: "3 tomatoes",
        userVerified: true,
      },
    ],
    nutritionalSummary: {
      calories: 350,
      protein: 15,
      carbs: 30,
      fat: 20,
      fiber: 8,
      sugar: 3,
      sodium: 400,
    },
  },
];

// Mock Gallery Collection
export const mockGallery = [
  {
    id: "g1",
    title: "Colorful Breakfast Bowl",
    description: "Start your day with a nutritious and colorful breakfast bowl packed with vitamins and protein.",
    imageUrl: "https://images.unsplash.com/photo-1494597564530-871f2b93ac55?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80",
    analysisId: "fa1",
    featured: true,
    displayOrder: 1,
  },
  {
    id: "g2",
    title: "Perfect Protein Lunch",
    description: "A balanced lunch with lean protein and fresh vegetables to keep you energized throughout the day.",
    imageUrl: "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80",
    analysisId: "fa2",
    featured: true,
    displayOrder: 2,
  },
  {
    id: "g3",
    title: "Omega-Rich Dinner",
    description: "Enjoy the benefits of omega-3 fatty acids with this delicious and nutritious dinner option.",
    imageUrl: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80",
    analysisId: "fa3",
    featured: false,
    displayOrder: 3,
  },
  {
    id: "g4",
    title: "Smart Snacking",
    description: "Healthy snack options that satisfy cravings while providing essential nutrients.",
    imageUrl: "https://images.unsplash.com/photo-1525385133512-2f3bdd039054?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80",
    analysisId: "fa4",
    featured: true,
    displayOrder: 4,
  },
];

// Mock Announcements Collection
export const mockAnnouncements = [
  {
    id: "a1",
    title: "New Food Recognition Engine",
    content: "We've upgraded our AI food recognition system to identify over 5,000 food items with greater accuracy. Try it out today!",
    category: "feature",
    importance: "high",
    publishDate: "2023-04-30T00:00:00Z",
    expiryDate: "2023-06-30T00:00:00Z",
  },
  {
    id: "a2",
    title: "Summer Challenge Starting Soon",
    content: "Join our 30-day summer nutrition challenge starting June 1st. Track your meals consistently to win prizes and improve your health!",
    category: "event",
    importance: "medium",
    publishDate: "2023-05-15T00:00:00Z",
    expiryDate: "2023-06-01T00:00:00Z",
  },
  {
    id: "a3",
    title: "App Update v2.5 Released",
    content: "We've added new features including customizable dashboard, improved nutritional insights, and meal planning tools.",
    category: "update",
    importance: "medium",
    publishDate: "2023-05-01T00:00:00Z",
    expiryDate: "2023-05-30T00:00:00Z",
  },
];

// Mock Food Database (simplified)
export const mockFoodDatabase = [
  {
    id: "fd1",
    name: "Apple",
    category: "Fruits",
    nutrientsPerServing: {
      servingSize: "1 medium (182g)",
      calories: 95,
      protein: 0.5,
      carbs: 25,
      fat: 0.3,
      fiber: 4.4,
      sugar: 19,
      sodium: 2,
      vitamin_a: 2,
      vitamin_c: 14,
      calcium: 1,
      iron: 1,
    },
    commonPortions: [
      "1 small",
      "1 medium",
      "1 large",
      "1 cup, sliced",
    ],
  },
  {
    id: "fd2",
    name: "Grilled Chicken Breast",
    category: "Poultry",
    nutrientsPerServing: {
      servingSize: "1 breast (172g)",
      calories: 284,
      protein: 53.4,
      carbs: 0,
      fat: 6.2,
      fiber: 0,
      sugar: 0,
      sodium: 130,
      vitamin_a: 0,
      vitamin_c: 0,
      calcium: 1,
      iron: 5,
    },
    commonPortions: [
      "1/2 breast",
      "1 breast",
      "100g",
    ],
  },
  {
    id: "fd3",
    name: "Avocado",
    category: "Fruits",
    nutrientsPerServing: {
      servingSize: "1 avocado (200g)",
      calories: 322,
      protein: 4,
      carbs: 17,
      fat: 30,
      fiber: 13.5,
      sugar: 1.3,
      sodium: 14,
      vitamin_a: 4,
      vitamin_c: 33,
      calcium: 2,
      iron: 6,
    },
    commonPortions: [
      "1/4 avocado",
      "1/2 avocado",
      "1 whole avocado",
    ],
  },
];

// Meal categories for dropdown options
export const mealCategories = [
  "breakfast",
  "lunch",
  "dinner",
  "snack",
  "dessert",
  "other",
];

// Dietary restriction options
export const dietaryRestrictions = [
  "vegetarian",
  "vegan",
  "gluten-free",
  "dairy-free",
  "nut-free",
  "keto",
  "paleo",
  "pescatarian",
  "halal",
  "kosher",
];

// Note: Mock users and login removed - now using real backend authentication
