import React, { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import { MessageCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OnlineUsersButtonProps {
  className?: string;
}

const OnlineUsersButton: React.FC<OnlineUsersButtonProps> = ({ className }) => {
  const [onlineCount, setOnlineCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);

  useEffect(() => {
    // Create socket connection
    const newSocket = io('http://localhost:5001', {
      transports: ['websocket', 'polling']
    });

    setSocket(newSocket);

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('Connected to WebSocket server');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
      setIsConnected(false);
    });

    // Listen for online count updates
    newSocket.on('onlineCount', (count: number) => {
      console.log('Online count updated:', count);
      setOnlineCount(count);
    });

    // Heartbeat to keep connection alive
    const heartbeatInterval = setInterval(() => {
      if (newSocket.connected) {
        newSocket.emit('heartbeat');
      }
    }, 30000); // Every 30 seconds

    // Cleanup on unmount
    return () => {
      clearInterval(heartbeatInterval);
      newSocket.disconnect();
    };
  }, []);

  return (
    <button
      className={cn(
        "inline-flex items-center gap-2 px-3 py-2 rounded-full",
        "bg-black text-white text-sm font-medium",
        "hover:bg-gray-800 transition-colors duration-200",
        "border border-gray-700",
        className
      )}
      onClick={() => {
        // Optional: Add click handler for future functionality
        console.log('Online users button clicked');
      }}
    >
      {/* Green dot indicator */}
      <div 
        className={cn(
          "w-2 h-2 rounded-full",
          isConnected ? "bg-green-500" : "bg-gray-500",
          isConnected && "animate-pulse"
        )}
      />
      
      {/* Online count */}
      <span className="font-semibold">
        {onlineCount}
      </span>
      
      {/* "online" text */}
      <span className="text-gray-300">
        online
      </span>
      
      {/* Message icon */}
      <MessageCircle className="w-4 h-4 text-gray-300" />
    </button>
  );
};

export default OnlineUsersButton;
