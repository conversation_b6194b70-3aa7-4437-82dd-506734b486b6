import React, { useState, useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { MessageCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OnlineUsersButtonProps {
  className?: string;
}

const OnlineUsersButton: React.FC<OnlineUsersButtonProps> = ({ className }) => {
  const [onlineCount, setOnlineCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);

  // Drag functionality state
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isInitialized, setIsInitialized] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Initialize position on mount
  useEffect(() => {
    if (!isInitialized) {
      // Set initial position to top-right corner
      const initialX = window.innerWidth - 200; // 200px from right edge
      const initialY = 20; // 20px from top

      setPosition({ x: initialX, y: initialY });
      setIsInitialized(true);
    }
  }, [isInitialized]);

  useEffect(() => {
    // Create socket connection
    const newSocket = io('http://localhost:5001', {
      transports: ['websocket', 'polling']
    });

    setSocket(newSocket);

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('Connected to WebSocket server');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
      setIsConnected(false);
    });

    // Listen for online count updates
    newSocket.on('onlineCount', (count: number) => {
      console.log('Online count updated:', count);
      setOnlineCount(count);
    });

    // Heartbeat to keep connection alive
    const heartbeatInterval = setInterval(() => {
      if (newSocket.connected) {
        newSocket.emit('heartbeat');
      }
    }, 30000); // Every 30 seconds

    // Cleanup on unmount
    return () => {
      clearInterval(heartbeatInterval);
      newSocket.disconnect();
    };
  }, []);

  // Drag event handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!buttonRef.current) return;

    const rect = buttonRef.current.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
    setIsDragging(true);

    // Prevent text selection during drag
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    const newX = e.clientX - dragOffset.x;
    const newY = e.clientY - dragOffset.y;

    // Keep button within viewport bounds
    const maxX = window.innerWidth - (buttonRef.current?.offsetWidth || 0);
    const maxY = window.innerHeight - (buttonRef.current?.offsetHeight || 0);

    setPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  return (
    <button
      ref={buttonRef}
      className={cn(
        "inline-flex items-center gap-2 px-3 py-2 rounded-full",
        "bg-black text-white text-sm font-medium",
        "hover:bg-gray-800 transition-colors duration-200",
        "border border-gray-700",
        "cursor-move select-none", // Add cursor and prevent text selection
        isDragging && "cursor-grabbing", // Change cursor when dragging
        className
      )}
      style={{
        position: 'fixed',
        left: `${position.x}px`,
        top: `${position.y}px`,
        zIndex: 1000,
        transform: isDragging ? 'scale(1.05)' : 'scale(1)', // Slight scale when dragging
        transition: isDragging ? 'none' : 'transform 0.2s ease',
      }}
      onMouseDown={handleMouseDown}
      onClick={(e) => {
        // Prevent click if we just finished dragging
        if (isDragging) {
          e.preventDefault();
          return;
        }
        // Optional: Add click handler for future functionality
        console.log('Online users button clicked');
      }}
    >
      {/* Green dot indicator */}
      <div 
        className={cn(
          "w-2 h-2 rounded-full",
          isConnected ? "bg-green-500" : "bg-gray-500",
          isConnected && "animate-pulse"
        )}
      />
      
      {/* Online count */}
      <span className="font-semibold">
        {onlineCount}
      </span>
      
      {/* "online" text */}
      <span className="text-gray-300">
        online
      </span>
      
      {/* Message icon */}
      <MessageCircle className="w-4 h-4 text-gray-300" />
    </button>
  );
};

export default OnlineUsersButton;
