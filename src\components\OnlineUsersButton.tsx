import React, { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import { Users } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OnlineUsersButtonProps {
  className?: string;
}

const OnlineUsersButton: React.FC<OnlineUsersButtonProps> = ({ className }) => {
  const [onlineCount, setOnlineCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);

  useEffect(() => {
    // Create socket connection
    const newSocket = io('http://localhost:5000', {
      transports: ['websocket', 'polling']
    });

    setSocket(newSocket);

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('Connected to WebSocket server');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
      setIsConnected(false);
    });

    // Listen for online count updates
    newSocket.on('onlineCount', (count: number) => {
      console.log('Online count updated:', count);
      setOnlineCount(count);
    });

    // Heartbeat to keep connection alive
    const heartbeatInterval = setInterval(() => {
      if (newSocket.connected) {
        newSocket.emit('heartbeat');
      }
    }, 30000); // Every 30 seconds

    // Cleanup on unmount
    return () => {
      clearInterval(heartbeatInterval);
      newSocket.disconnect();
    };
  }, []);

  return (
    <div
      className={cn(
        "fixed bottom-4 right-4 z-50", // Fixed position at bottom right
        "inline-flex items-center gap-2 px-4 py-2 rounded-full",
        "bg-white/95 backdrop-blur-sm text-nutrisnap-charcoal text-sm font-medium",
        "border border-nutrisnap-teal/20 shadow-lg",
        "hover:bg-white hover:shadow-xl transition-all duration-300",
        "animate-fade-in", // Use landing page animation
        className
      )}
    >
      {/* Connection status dot */}
      <div
        className={cn(
          "w-2 h-2 rounded-full",
          isConnected ? "bg-nutrisnap-teal" : "bg-gray-400",
          isConnected && "animate-pulse"
        )}
      />

      {/* Online count */}
      <span className="font-semibold text-nutrisnap-charcoal">
        {onlineCount}
      </span>

      {/* "online" text */}
      <span className="text-gray-600 font-medium">
        online
      </span>

      {/* Users icon */}
      <Users className="w-4 h-4 text-nutrisnap-teal" />
    </div>
  );
};

export default OnlineUsersButton;
