
import { useLocation, <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { useEffect } from "react";
import { Home } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4">
      <div className="text-center space-y-6 max-w-md">
        <div className="bg-nutrisnap-teal/10 rounded-full p-4 w-20 h-20 flex items-center justify-center mx-auto">
          <span className="text-nutrisnap-teal text-4xl font-bold">404</span>
        </div>
        <h1 className="text-3xl font-bold text-nutrisnap-charcoal">
          Page not found
        </h1>
        <p className="text-gray-600">
          Sorry, we couldn't find the page you're looking for. It might have been moved or deleted.
        </p>
        <Button asChild className="bg-nutrisnap-teal hover:bg-nutrisnap-teal/90">
          <Link to="/">
            <Home className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
