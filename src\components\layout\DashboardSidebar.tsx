
import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import {
  Camera,
  Home,
  PieChart,
  Calendar,
  Settings,
  History,
  BarChart,
  LogOut,
  Users,
  Shield,
  FileText,
  Database,
  Activity,
} from "lucide-react";

type SidebarItemProps = {
  icon: React.ElementType;
  label: string;
  href: string;
  active?: boolean;
  onClick?: () => void;
};

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon: Icon,
  label,
  href,
  active,
  onClick,
}) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium w-full transition-colors",
        active
          ? "bg-nutrisnap-teal text-white"
          : "text-gray-500 hover:text-nutrisnap-teal hover:bg-gray-100"
      )}
    >
      <Icon className="h-5 w-5" />
      <span>{label}</span>
    </button>
  );
};

const DashboardSidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, user } = useAuth();

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  const isActive = (path: string) => location.pathname === path;

  // Helper function to check user permissions
  const hasRole = (role: 'admin' | 'editor') => {
    if (!user) return false;

    // Check if user is admin based on email (fallback for compatibility)
    const isAdminByEmail = user.email === '<EMAIL>';

    // Multiple ways to check admin status
    const isAdmin = user.role === 'admin' || user.isAdmin || isAdminByEmail;
    const isEditor = user.role === 'editor' || isAdmin;

    if (role === 'admin') return isAdmin;
    if (role === 'editor') return isEditor;
    return false;
  };

  return (
    <div className="w-64 border-r border-gray-200 min-h-screen bg-white">
      <div className="flex flex-col h-full">
        <div className="p-4 border-b">
          <div className="flex items-center">
            <Camera className="h-8 w-8 text-nutrisnap-teal" />
            <span className="ml-2 text-xl font-bold text-nutrisnap-charcoal">
              NutriSnap
            </span>
          </div>
          {user && (
            <div className="mt-4 flex items-center">
              <div className="w-10 h-10 rounded-full bg-nutrisnap-teal flex items-center justify-center text-white font-semibold">
                {user.firstName.charAt(0)}
                {user.lastName.charAt(0)}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-700">
                  {user.firstName} {user.lastName}
                </p>
                <p className="text-xs text-gray-500">{user.email}</p>
              </div>
            </div>
          )}
        </div>

        <nav className="flex-1 overflow-y-auto py-4 px-2 space-y-1">
          <SidebarItem
            icon={Home}
            label="Dashboard"
            href="/dashboard"
            active={isActive("/dashboard")}
            onClick={() => navigate("/dashboard")}
          />
          <SidebarItem
            icon={PieChart}
            label="Nutrition Summary"
            href="/nutrition-summary"
            active={isActive("/nutrition-summary")}
            onClick={() => navigate("/nutrition-summary")}
          />
          <SidebarItem
            icon={BarChart}
            label="Status & Trends"
            href="/status-trends"
            active={isActive("/status-trends")}
            onClick={() => navigate("/status-trends")}
          />
          <SidebarItem
            icon={Camera}
            label="Snap New"
            href="/snap-new"
            active={isActive("/snap-new")}
            onClick={() => navigate("/snap-new")}
          />
          <SidebarItem
            icon={History}
            label="Meal History"
            href="/meal-history"
            active={isActive("/meal-history")}
            onClick={() => navigate("/meal-history")}
          />
          <SidebarItem
            icon={Settings}
            label="Settings"
            href="/settings"
            active={isActive("/settings")}
            onClick={() => navigate("/settings")}
          />

          {/* Editor/Admin Content Management */}
          {(hasRole('editor') || user?.email === '<EMAIL>') && (
            <>
              <div className="pt-4 pb-2">
                <p className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Content Management
                </p>
              </div>
              <SidebarItem
                icon={Camera}
                label="Gallery Management"
                href="/admin/gallery"
                active={isActive("/admin/gallery")}
                onClick={() => navigate("/admin/gallery")}
              />
              <SidebarItem
                icon={Calendar}
                label="Announcements"
                href="/admin/announcements"
                active={isActive("/admin/announcements")}
                onClick={() => navigate("/admin/announcements")}
              />
            </>
          )}

          {/* Admin Only Features */}
          {(hasRole('admin') || user?.email === '<EMAIL>') && (
            <>
              <div className="pt-4 pb-2">
                <p className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Administration
                </p>
              </div>
              <SidebarItem
                icon={Shield}
                label="Admin Dashboard"
                href="/admin/dashboard"
                active={isActive("/admin/dashboard")}
                onClick={() => navigate("/admin/dashboard")}
              />
              <SidebarItem
                icon={Users}
                label="User Management"
                href="/admin/users"
                active={isActive("/admin/users")}
                onClick={() => navigate("/admin/users")}
              />
              <SidebarItem
                icon={Database}
                label="System Config"
                href="/admin/config"
                active={isActive("/admin/config")}
                onClick={() => navigate("/admin/config")}
              />
              <SidebarItem
                icon={Activity}
                label="System Logs"
                href="/admin/logs"
                active={isActive("/admin/logs")}
                onClick={() => navigate("/admin/logs")}
              />
            </>
          )}
        </nav>

        <div className="p-4 border-t">
          <SidebarItem
            icon={LogOut}
            label="Logout"
            href="/"
            onClick={handleLogout}
          />
        </div>
      </div>
    </div>
  );
};

export default DashboardSidebar;
