
import React from "react";
import { Outlet } from "react-router-dom";
import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
} from "@/components/ui/sidebar";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Camera,
  Home,
  PieChart,
  Calendar,
  Settings,
  History,
  BarChart,
  LogOut,
  Shield,
  Users,
  Cog,
  FileText,
} from "lucide-react";

export const DashboardLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <AppSidebar />
        <main className="flex-1 overflow-x-hidden">
          {children}
        </main>
      </div>
    </SidebarProvider>
  );
};

const AppSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, user } = useAuth();

  const isActive = (path: string) => location.pathname === path;

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="flex items-center p-2">
          <Camera className="h-8 w-8 text-nutrisnap-teal" />
          <span className="ml-2 text-xl font-bold text-nutrisnap-charcoal">
            NutriSnap
          </span>
        </div>
        {user && (
          <div className="px-2 pt-2 flex items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold ${
              user.role === 'admin' ? 'bg-red-600' :
              user.role === 'editor' ? 'bg-orange-500' :
              'bg-nutrisnap-teal'
            }`}>
              {user.firstName.charAt(0)}
              {user.lastName.charAt(0)}
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700">
                {user.firstName} {user.lastName}
              </p>
              <div className="flex items-center space-x-2">
                <p className="text-xs text-gray-500">{user.email}</p>
                {user.role === 'admin' && (
                  <span className="text-xs bg-red-100 text-red-800 px-2 py-0.5 rounded-full font-medium">
                    Admin
                  </span>
                )}
                {user.role === 'editor' && (
                  <span className="text-xs bg-orange-100 text-orange-800 px-2 py-0.5 rounded-full font-medium">
                    Editor
                  </span>
                )}
              </div>
            </div>
          </div>
        )}
      </SidebarHeader>

      <SidebarContent>
        {/* Show regular navigation only for regular users (not admin or editor) */}
        {user?.role === 'user' && (
          <SidebarGroup>
            <SidebarGroupLabel>Navigation</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton
                    isActive={isActive("/dashboard")}
                    onClick={() => navigate("/dashboard")}
                    tooltip="Dashboard"
                  >
                    <Home className="h-5 w-5" />
                    <span>Dashboard</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <SidebarMenuItem>
                  <SidebarMenuButton
                    isActive={isActive("/nutrition-summary")}
                    onClick={() => navigate("/nutrition-summary")}
                    tooltip="Nutrition Summary"
                  >
                    <PieChart className="h-5 w-5" />
                    <span>Nutrition Summary</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <SidebarMenuItem>
                  <SidebarMenuButton
                    isActive={isActive("/status-trends")}
                    onClick={() => navigate("/status-trends")}
                    tooltip="Status & Trends"
                  >
                    <BarChart className="h-5 w-5" />
                    <span>Status & Trends</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <SidebarMenuItem>
                  <SidebarMenuButton
                    isActive={isActive("/snap-new")}
                    onClick={() => navigate("/snap-new")}
                    tooltip="Snap New"
                  >
                    <Camera className="h-5 w-5" />
                    <span>Snap New</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <SidebarMenuItem>
                  <SidebarMenuButton
                    isActive={isActive("/meal-history")}
                    onClick={() => navigate("/meal-history")}
                    tooltip="Meal History"
                  >
                    <History className="h-5 w-5" />
                    <span>History</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <SidebarMenuItem>
                  <SidebarMenuButton
                    isActive={isActive("/settings")}
                    onClick={() => navigate("/settings")}
                    tooltip="Settings"
                  >
                    <Settings className="h-5 w-5" />
                    <span>Settings</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {user?.canAccessAdminPanel && (
          <SidebarGroup>
            <SidebarGroupLabel>Admin Panel</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton
                    isActive={isActive("/admin/dashboard")}
                    onClick={() => navigate("/admin/dashboard")}
                    tooltip="Admin Dashboard"
                  >
                    <Shield className="h-5 w-5" />
                    <span>Dashboard</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                {/* Editors focus on content management only - no personal nutrition access */}

                {(user.role === 'editor' || user.role === 'admin') && (
                  <>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        isActive={isActive("/admin/gallery")}
                        onClick={() => navigate("/admin/gallery")}
                        tooltip="Gallery Management"
                      >
                        <Camera className="h-5 w-5" />
                        <span>Gallery Management</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        isActive={isActive("/admin/announcements")}
                        onClick={() => navigate("/admin/announcements")}
                        tooltip="Announcements"
                      >
                        <Calendar className="h-5 w-5" />
                        <span>Announcements</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </>
                )}

                {user.role === 'admin' && (
                  <>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        isActive={isActive("/admin/users")}
                        onClick={() => navigate("/admin/users")}
                        tooltip="User Management"
                      >
                        <Users className="h-5 w-5" />
                        <span>User Management</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        isActive={isActive("/admin/config")}
                        onClick={() => navigate("/admin/config")}
                        tooltip="System Configuration"
                      >
                        <Cog className="h-5 w-5" />
                        <span>System Config</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        isActive={isActive("/admin/logs")}
                        onClick={() => navigate("/admin/logs")}
                        tooltip="System Logs"
                      >
                        <FileText className="h-5 w-5" />
                        <span>System Logs</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              onClick={handleLogout}
              tooltip="Logout"
            >
              <LogOut className="h-5 w-5" />
              <span>Logout</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
};

export default DashboardLayout;
