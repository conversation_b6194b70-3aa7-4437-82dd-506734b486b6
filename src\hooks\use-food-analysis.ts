import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import FoodAnalysisService, { UploadFoodAnalysisData } from '@/services/food-analysis.service';
import { toast } from 'sonner';

export const useFoodAnalyses = (params?: {
  startDate?: string;
  endDate?: string;
  mealCategory?: string;
}) => {
  return useQuery({
    queryKey: ['foodAnalyses', params?.startDate, params?.endDate, params?.mealCategory],
    queryFn: () => FoodAnalysisService.getAllAnalyses(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useFoodAnalysis = (id: string) => {
  return useQuery({
    queryKey: ['foodAnalysis', id],
    queryFn: () => FoodAnalysisService.getAnalysisById(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id,
  });
};

export const useUploadFoodAnalysis = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: UploadFoodAnalysisData) => FoodAnalysisService.uploadAnalysis(data),
    onSuccess: () => {
      // Invalidate food analyses query to refetch
      queryClient.invalidateQueries({ queryKey: ['foodAnalyses'] });
      
      toast.success('Food analysis uploaded successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to upload food analysis');
    }
  });
};

export const useUpdateFoodAnalysis = (id: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: {
      mealCategory?: string;
      mealDateTime?: string;
      userNotes?: string;
      recognitionResults?: any[];
      nutritionalSummary?: {
        calories: number;
        protein: number;
        carbs: number;
        fat: number;
        fiber: number;
        sugar: number;
        sodium: number;
      };
    }) => FoodAnalysisService.updateAnalysis(id, data),
    onSuccess: () => {
      // Invalidate specific food analysis query to refetch
      queryClient.invalidateQueries({ queryKey: ['foodAnalysis', id] });
      // Invalidate food analyses list
      queryClient.invalidateQueries({ queryKey: ['foodAnalyses'] });
      
      toast.success('Food analysis updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update food analysis');
    }
  });
};

export const useDeleteFoodAnalysis = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => FoodAnalysisService.deleteAnalysis(id),
    onSuccess: () => {
      // Invalidate food analyses query to refetch
      queryClient.invalidateQueries({ queryKey: ['foodAnalyses'] });
      
      toast.success('Food analysis deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete food analysis');
    }
  });
};
