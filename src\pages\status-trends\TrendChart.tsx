import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { TrendData } from '../StatusTrends';
import { formatDisplayNumber, formatCalories } from '@/utils/numberUtils';
import { TrendingUp, Activity, Zap, Droplets } from 'lucide-react';

interface TrendChartProps {
  data: TrendData[];
  period: number;
}

type MetricType = 'calories' | 'protein' | 'carbs' | 'fat';

const TrendChart: React.FC<TrendChartProps> = ({ data, period }) => {
  const [selectedMetric, setSelectedMetric] = useState<MetricType>('calories');

  // Metric configuration
  const metrics = {
    calories: {
      label: 'Calories',
      color: '#3b82f6',
      icon: Zap,
      unit: 'kcal',
      formatter: formatCalories,
    },
    protein: {
      label: 'Protein',
      color: '#ef4444',
      icon: Activity,
      unit: 'g',
      formatter: formatDisplayNumber,
    },
    carbs: {
      label: 'Carbohydrates',
      color: '#22c55e',
      icon: TrendingUp,
      unit: 'g',
      formatter: formatDisplayNumber,
    },
    fat: {
      label: 'Fat',
      color: '#eab308',
      icon: Droplets,
      unit: 'g',
      formatter: formatDisplayNumber,
    },
  };

  // Format data for chart
  const chartData = data.map(item => ({
    ...item,
    date: new Date(item.date).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    }),
    formattedCalories: formatCalories(item.calories),
    formattedProtein: formatDisplayNumber(item.protein),
    formattedCarbs: formatDisplayNumber(item.carbs),
    formattedFat: formatDisplayNumber(item.fat),
  }));

  // Calculate trend statistics
  const currentMetric = metrics[selectedMetric];
  const values = data.map(item => item[selectedMetric]);
  const average = values.reduce((sum, val) => sum + val, 0) / values.length;
  const trend = values.length > 1 ? values[values.length - 1] - values[0] : 0;
  const trendPercentage = values[0] !== 0 ? (trend / values[0]) * 100 : 0;

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          <div className="space-y-1 mt-2">
            <p className="text-blue-600">
              Calories: {data.formattedCalories} kcal
            </p>
            <p className="text-red-600">
              Protein: {data.formattedProtein}g
            </p>
            <p className="text-green-600">
              Carbs: {data.formattedCarbs}g
            </p>
            <p className="text-yellow-600">
              Fat: {data.formattedFat}g
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <currentMetric.icon className="h-5 w-5" />
            Nutrition Trends ({period} days)
          </CardTitle>
          
          {/* Metric Selection */}
          <div className="flex gap-1">
            {Object.entries(metrics).map(([key, metric]) => (
              <Button
                key={key}
                variant={selectedMetric === key ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedMetric(key as MetricType)}
                className={`text-xs ${
                  selectedMetric === key 
                    ? 'bg-nutrisnap-teal' 
                    : 'hover:bg-gray-100'
                }`}
              >
                <metric.icon className="h-3 w-3 mr-1" />
                {metric.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Trend Statistics */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">Average</p>
            <p className="text-lg font-semibold" style={{ color: currentMetric.color }}>
              {currentMetric.formatter(average)} {currentMetric.unit}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Trend</p>
            <p className={`text-lg font-semibold ${
              trend >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {trend >= 0 ? '+' : ''}{currentMetric.formatter(trend)} {currentMetric.unit}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Change</p>
            <p className={`text-lg font-semibold ${
              trendPercentage >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {trendPercentage >= 0 ? '+' : ''}{formatDisplayNumber(trendPercentage)}%
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="date" 
                stroke="#666"
                fontSize={12}
              />
              <YAxis 
                stroke="#666"
                fontSize={12}
                tickFormatter={(value) => currentMetric.formatter(value)}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey={selectedMetric}
                stroke={currentMetric.color}
                strokeWidth={3}
                dot={{ fill: currentMetric.color, strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: currentMetric.color, strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Chart Legend */}
        <div className="mt-4 flex justify-center">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: currentMetric.color }}
            />
            <span>{currentMetric.label} over {period} days</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TrendChart;
