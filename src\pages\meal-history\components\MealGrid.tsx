
import React from "react";
import FoodCard from "@/components/food/FoodCard";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { FoodAnalysis } from "@/types/meal";

interface MealGridProps {
  meals: FoodAnalysis[];
  searchTerm: string;
  selectedCategory: string | null;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

const MealGrid: React.FC<MealGridProps> = ({ 
  meals, 
  searchTerm,
  selectedCategory,
  dateRange
}) => {
  if (meals.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-12 text-center">
        <div className="text-gray-400 mb-4">
          <Search className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-800 mb-2">
          No meals found
        </h3>
        <p className="text-gray-500 mb-6">
          {searchTerm || selectedCategory || dateRange.startDate
            ? "Try adjusting your search filters"
            : "You haven't logged any meals yet"}
        </p>
        <Button>Snap a New Meal</Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {meals.map((meal) => (
        <FoodCard
          key={meal.id}
          id={meal.id}
          imageUrl={meal.imageUrl}
          title={meal.userNotes}
          mealCategory={meal.mealCategory}
          date={meal.mealDateTime}
          calories={meal.nutritionalSummary.calories}
        />
      ))}
    </div>
  );
};

export default MealGrid;
