import React from 'react';
import { useAdminDashboard, useLogStats, useRecentLogs } from '@/hooks/use-admin';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  Camera,
  Calendar,
  FileImage,
  AlertCircle,
  TrendingUp,
  Activity,
  Shield,
  Edit
} from 'lucide-react';
import { format } from 'date-fns';

const AdminDashboard = () => {
  const { user } = useAuth();
  const { data: dashboardData, isLoading: isDashboardLoading, error: dashboardError } = useAdminDashboard();

  // Only load log data for admins
  const { data: logStats, isLoading: isLogStatsLoading } = useLogStats();
  const { data: recentLogs, isLoading: isRecentLogsLoading } = useRecentLogs(5);

  const isAdmin = user?.role === 'admin';
  const isEditor = user?.role === 'editor';

  // Debug logging for dashboard data (only if there's an issue)
  if (dashboardError || (!isDashboardLoading && (!dashboardData || dashboardData.totalUsers === 0))) {
    console.log('🔍 Admin Dashboard Debug:');
    console.log('- Dashboard data:', dashboardData);
    console.log('- Dashboard loading:', isDashboardLoading);
    console.log('- Dashboard error:', dashboardError);
    console.log('- User role:', user?.role);
  }

  // Debug recent logs (only if there's an issue)
  if (isAdmin && (!isRecentLogsLoading && (!recentLogs || recentLogs.length === 0))) {
    console.log('🔍 Recent Logs Debug:');
    console.log('- Recent logs data:', recentLogs);
    console.log('- Recent logs loading:', isRecentLogsLoading);
    console.log('- Recent logs length:', recentLogs?.length);
  }

  if (dashboardError) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load admin dashboard data. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'destructive';
      case 'warn': return 'secondary';
      case 'info': return 'default';
      case 'debug': return 'outline';
      default: return 'default';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditor ? 'Content Management Dashboard' : 'Admin Dashboard'}
          </h1>
          <p className="text-gray-600 mt-1">
            {isEditor ? 'Content overview and management' : 'System overview and management'}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {isAdmin ? (
            <>
              <Shield className="h-5 w-5 text-red-600" />
              <span className="text-sm font-medium text-gray-700">Administrator</span>
            </>
          ) : (
            <>
              <Edit className="h-5 w-5 text-orange-500" />
              <span className="text-sm font-medium text-gray-700">Content Editor</span>
            </>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className={`grid grid-cols-1 md:grid-cols-2 ${isEditor ? 'lg:grid-cols-3' : 'lg:grid-cols-4'} gap-6`}>
        {/* Only show user stats to admins */}
        {isAdmin && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {isDashboardLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <>
                  <div className="text-2xl font-bold">{dashboardData?.totalUsers || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Active user accounts
                  </p>
                </>
              )}
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Food Analyses</CardTitle>
            <Camera className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isDashboardLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{dashboardData?.totalFoodAnalyses || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Total food scans
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gallery Items</CardTitle>
            <FileImage className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isDashboardLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{dashboardData?.totalGalleryItems || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Published images
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Announcements</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isDashboardLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{dashboardData?.totalAnnouncements || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Active announcements
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      <div className={`grid grid-cols-1 ${isAdmin ? 'lg:grid-cols-2' : 'lg:grid-cols-1'} gap-6`}>
        {/* System Logs Overview - Admin Only */}
        {isAdmin && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>System Logs Overview</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLogStatsLoading ? (
                <div className="space-y-3">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ) : logStats ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{logStats.errorCount}</div>
                      <div className="text-sm text-gray-600">Errors</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{logStats.warningCount}</div>
                      <div className="text-sm text-gray-600">Warnings</div>
                    </div>
                  </div>
                  <div className="text-center pt-2 border-t">
                    <div className="text-lg font-semibold">{logStats.totalLogs}</div>
                    <div className="text-sm text-gray-600">Total Log Entries</div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No log data available</p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>{isEditor ? 'Recent Content Activity' : 'Recent Activity'}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isDashboardLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-3 w-1/2 mt-1" />
                    </div>
                  </div>
                ))}
              </div>
            ) : dashboardData?.recentActivity && dashboardData.recentActivity.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.recentActivity.map((activity) => {
                  const getActivityIcon = (type: string) => {
                    switch (type) {
                      case 'user_registration':
                        return <Users className="h-4 w-4 text-blue-500" />;
                      case 'food_analysis':
                        return <Camera className="h-4 w-4 text-green-500" />;
                      case 'gallery_upload':
                        return <FileImage className="h-4 w-4 text-purple-500" />;
                      case 'announcement':
                        return <Calendar className="h-4 w-4 text-orange-500" />;
                      default:
                        return <Activity className="h-4 w-4 text-gray-500" />;
                    }
                  };

                  return (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className="mt-1">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.description}</p>
                        {activity.user && (
                          <p className="text-xs text-gray-400">by {activity.user.name}</p>
                        )}
                        <p className="text-xs text-gray-500">
                          {(() => {
                            try {
                              const date = new Date(activity.timestamp);
                              if (isNaN(date.getTime())) {
                                return 'Invalid date';
                              }
                              return format(date, 'MMM dd, yyyy HH:mm');
                            } catch (error) {
                              console.error('Date formatting error:', error, activity.timestamp);
                              return 'Invalid date';
                            }
                          })()}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-gray-500">No recent activity</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent System Logs - Admin Only */}
      {isAdmin && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5" />
              <span>Recent System Logs</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isRecentLogsLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-4 flex-1" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                ))}
              </div>
            ) : recentLogs && recentLogs.length > 0 ? (
              <div className="space-y-3">
                {recentLogs.map((log) => (
                  <div key={log.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                    <div className="flex items-center space-x-3">
                      <Badge variant={getLogLevelColor(log.level) as any}>
                        {log.level.toUpperCase()}
                      </Badge>
                      <span className="text-sm">{log.message}</span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {(() => {
                        try {
                          const date = new Date(log.timestamp);
                          if (isNaN(date.getTime())) {
                            return 'Invalid time';
                          }
                          return format(date, 'HH:mm:ss');
                        } catch (error) {
                          console.error('Log date formatting error:', error, log.timestamp);
                          return 'Invalid time';
                        }
                      })()}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No recent logs available</p>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdminDashboard;
