
import React from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { Save } from "lucide-react";

interface AccountActionsSectionProps {
  loading: boolean;
}

const AccountActionsSection: React.FC<AccountActionsSectionProps> = ({ loading }) => {
  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleDeleteAccount = () => {
    // Show confirmation first in a real app
    setTimeout(() => {
      toast.success("Account deleted successfully");
      logout();
      navigate("/");
    }, 1000);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Account Actions</CardTitle>
        <CardDescription>
          Manage your account or delete it permanently
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-red-600">
              Delete Account
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Once you delete your account, there is no going back.
              Please be certain.
            </p>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteAccount}
            >
              Delete Account
            </Button>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end border-t pt-6">
        <Button type="submit" disabled={loading}>
          {loading ? (
            <span className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
              Saving...
            </span>
          ) : (
            <span className="flex items-center">
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </span>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default AccountActionsSection;
