
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { Camera, Utensils, Clock } from "lucide-react";

const QuickActions = () => {
  const navigate = useNavigate();

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
      <Card className="bg-gradient-to-br from-nutrisnap-teal to-nutrisnap-teal/80 text-white hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Snap New Meal</CardTitle>
          <CardDescription className="text-white/70">
            Quickly analyze your food
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant="secondary"
            className="w-full bg-white/20 hover:bg-white/30 text-white"
            onClick={() => navigate("/snap-new")}
          >
            <Camera className="mr-2 h-4 w-4" />
            Snap Now
          </Button>
        </CardContent>
      </Card>
      <Card className="bg-gradient-to-br from-nutrisnap-purple to-nutrisnap-purple/80 text-white hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">View Nutrition</CardTitle>
          <CardDescription className="text-white/70">
            Check your detailed nutrition
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant="secondary"
            className="w-full bg-white/20 hover:bg-white/30 text-white"
            onClick={() => navigate("/nutrition-summary")}
          >
            <Utensils className="mr-2 h-4 w-4" />
            View Summary
          </Button>
        </CardContent>
      </Card>
      <Card className="bg-gradient-to-br from-nutrisnap-orange to-nutrisnap-orange/80 text-white hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Meal History</CardTitle>
          <CardDescription className="text-white/70">
            Browse your previous meals
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant="secondary"
            className="w-full bg-white/20 hover:bg-white/30 text-white"
            onClick={() => navigate("/meal-history")}
          >
            <Clock className="mr-2 h-4 w-4" />
            View History
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuickActions;
