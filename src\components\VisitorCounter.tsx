import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Eye, Calendar, TrendingUp, Wifi } from 'lucide-react';
import { useVisitorTracking } from '@/hooks/use-visitor-stats';
import { cn } from '@/lib/utils';

interface CounterItemProps {
  icon: React.ReactNode;
  label: string;
  value: number;
  color: string;
  delay?: number;
}

const CounterItem: React.FC<CounterItemProps> = ({ icon, label, value, color, delay = 0 }) => {
  const [displayValue, setDisplayValue] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAnimating(true);
      const duration = 2000; // 2 seconds
      const steps = 60;
      const increment = value / steps;
      let current = 0;

      const counter = setInterval(() => {
        current += increment;
        if (current >= value) {
          setDisplayValue(value);
          clearInterval(counter);
          setIsAnimating(false);
        } else {
          setDisplayValue(Math.floor(current));
        }
      }, duration / steps);

      return () => clearInterval(counter);
    }, delay);

    return () => clearTimeout(timer);
  }, [value, delay]);

  return (
    <div className="flex items-center space-x-3 p-4 rounded-lg bg-gradient-to-r from-white/50 to-white/30 backdrop-blur-sm border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
      <div className={cn("p-2 rounded-full", color)}>
        {icon}
      </div>
      <div className="flex-1">
        <div className={cn(
          "text-2xl font-bold transition-all duration-300",
          isAnimating ? "text-nutrisnap-teal" : "text-gray-800"
        )}>
          {displayValue.toLocaleString()}
        </div>
        <div className="text-sm text-gray-600 font-medium">{label}</div>
      </div>
    </div>
  );
};

interface VisitorCounterProps {
  className?: string;
  showTitle?: boolean;
  compact?: boolean;
}

const VisitorCounter: React.FC<VisitorCounterProps> = ({ 
  className, 
  showTitle = true, 
  compact = false 
}) => {
  const { stats, isLoading, error } = useVisitorTracking(true);

  if (error) {
    return null; // Silently fail - visitor counter is not critical
  }

  if (isLoading || !stats) {
    return (
      <Card className={cn("bg-gradient-to-br from-nutrisnap-teal/10 to-blue-50 border-nutrisnap-teal/20", className)}>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="grid grid-cols-2 gap-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Ensure all values are numbers with fallbacks
  const safeStats = {
    onlineNow: stats.onlineNow || 0,
    todayVisitors: stats.todayVisitors || 0,
    weeklyVisitors: stats.weeklyVisitors || 0,
    totalVisitors: stats.totalVisitors || 0,
    lastUpdated: stats.lastUpdated || new Date().toISOString()
  };

  const counterItems = [
    {
      icon: <Wifi className="h-5 w-5 text-green-600" />,
      label: "Online Now",
      value: safeStats.onlineNow,
      color: "bg-green-100",
      delay: 0
    },
    {
      icon: <Calendar className="h-5 w-5 text-blue-600" />,
      label: "Today's Visitors",
      value: safeStats.todayVisitors,
      color: "bg-blue-100",
      delay: 200
    },
    {
      icon: <TrendingUp className="h-5 w-5 text-purple-600" />,
      label: "This Week",
      value: safeStats.weeklyVisitors,
      color: "bg-purple-100",
      delay: 400
    },
    {
      icon: <Users className="h-5 w-5 text-nutrisnap-teal" />,
      label: "Total Visitors",
      value: safeStats.totalVisitors,
      color: "bg-nutrisnap-teal/10",
      delay: 600
    }
  ];

  if (compact) {
    return (
      <div className={cn("flex items-center space-x-4", className)}>
        <Badge variant="secondary" className="bg-nutrisnap-teal/10 text-nutrisnap-teal border-nutrisnap-teal/20">
          <Eye className="h-3 w-3 mr-1" />
          {safeStats.totalVisitors.toLocaleString()} visitors
        </Badge>
        <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
          <Wifi className="h-3 w-3 mr-1" />
          {safeStats.onlineNow} online
        </Badge>
      </div>
    );
  }

  return (
    <Card className={cn(
      "bg-gradient-to-br from-nutrisnap-teal/10 via-blue-50 to-purple-50 border-nutrisnap-teal/20 shadow-lg hover:shadow-xl transition-all duration-300",
      className
    )}>
      <CardContent className="p-6">
        {showTitle && (
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
              <Eye className="h-5 w-5 mr-2 text-nutrisnap-teal" />
              Live Visitor Stats
            </h3>
            <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200 animate-pulse">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              Live
            </Badge>
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {counterItems.map((item, index) => (
            <CounterItem
              key={index}
              icon={item.icon}
              label={item.label}
              value={item.value}
              color={item.color}
              delay={item.delay}
            />
          ))}
        </div>

        <div className="mt-4 text-xs text-gray-500 text-center">
          Last updated: {new Date(safeStats.lastUpdated).toLocaleTimeString()}
        </div>
      </CardContent>
    </Card>
  );
};

export default VisitorCounter;
