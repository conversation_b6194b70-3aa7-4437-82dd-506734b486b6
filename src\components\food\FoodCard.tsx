import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useNavigate } from "react-router-dom";
import { getImageUrl } from "@/lib/image-utils";

type FoodCardProps = {
  id: string;
  imageUrl: string;
  title: string;
  mealCategory: string;
  date: string;
  calories: number;
  onClick?: () => void; // Added onClick as an optional prop
};

const FoodCard: React.FC<FoodCardProps> = ({
  id,
  imageUrl,
  title,
  mealCategory,
  date,
  calories,
  onClick,
}) => {
  const navigate = useNavigate();
  
  const formatMealCategory = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  };

  const getMealCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case "breakfast":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "lunch":
        return "bg-green-100 text-green-800 border-green-200";
      case "dinner":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "snack":
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleViewDetails = () => {
    if (onClick) {
      // Use the provided onClick handler if available
      onClick();
    } else {
      // Otherwise navigate to the meal details page
      navigate(`/meal-details/${id}`);
    }
  };

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow duration-300">
      <div className="relative h-40 overflow-hidden">
        <img
          src={getImageUrl(imageUrl)}
          alt={title}
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
        />
        <Badge
          className={`absolute top-2 right-2 ${getMealCategoryColor(
            mealCategory
          )}`}
        >
          {formatMealCategory(mealCategory)}
        </Badge>
      </div>
      <CardHeader className="p-4 pb-0">
        <CardTitle className="text-lg font-medium line-clamp-1">{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-2">
        <div className="flex justify-between text-sm text-gray-500">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1" />
            {getTimeAgo(date)}
          </div>
          <div>
            <span className="font-semibold">{calories}</span> cal
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        <Button
          variant="outline"
          className="w-full text-nutrisnap-teal border-nutrisnap-teal hover:bg-nutrisnap-teal hover:text-white"
          onClick={handleViewDetails}
        >
          View Details
        </Button>
      </CardFooter>
    </Card>
  );
};

export default FoodCard;
