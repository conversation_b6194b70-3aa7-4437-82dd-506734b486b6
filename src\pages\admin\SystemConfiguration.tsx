import React, { useState } from 'react';
import { useSystemConfigurations, useCreateConfiguration, useUpdateConfiguration, useDeleteConfiguration } from '@/hooks/use-admin';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Settings,
  AlertCircle,
  Database,
  Globe,
  Lock,
  Zap,
  Shield,
  Eye,
  EyeOff
} from 'lucide-react';
import { format } from 'date-fns';
import { SystemConfig, CreateSystemConfig } from '@/services/admin.service';

const SystemConfiguration = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<SystemConfig | null>(null);

  // Fetch configurations with filters
  const { data: configs, isLoading, error, refetch } = useSystemConfigurations({
    category: categoryFilter && categoryFilter !== 'all' ? categoryFilter : undefined,
  });

  const createMutation = useCreateConfiguration();
  const updateMutation = useUpdateConfiguration(editingConfig?.key || '');
  const deleteMutation = useDeleteConfiguration();

  // Filter configs by search term locally
  const filteredConfigs = Array.isArray(configs) ? configs.filter(config =>
    config.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
    config.description?.toLowerCase().includes(searchTerm.toLowerCase())
  ) : [];

  // Group configs by category
  const configsByCategory = filteredConfigs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, SystemConfig[]>);

  const categories = Object.keys(configsByCategory).sort();

  const handleCreateSubmit = async (formData: FormData) => {
    const key = formData.get('key') as string;
    const value = formData.get('value') as string;
    const description = formData.get('description') as string;
    const type = formData.get('type') as 'string' | 'number' | 'boolean' | 'object';
    const category = formData.get('category') as string;
    const isPublic = formData.get('isPublic') === 'on';

    if (!key || !value || !type || !category) {
      return;
    }

    let parsedValue: any = value;
    try {
      if (type === 'number') {
        parsedValue = parseFloat(value);
      } else if (type === 'boolean') {
        parsedValue = value === 'true';
      } else if (type === 'object') {
        parsedValue = JSON.parse(value);
      }
    } catch (error) {
      console.error('Failed to parse value:', error);
      return;
    }

    const createData: CreateSystemConfig = {
      key,
      value: parsedValue,
      description: description || undefined,
      type,
      category,
      isPublic,
    };

    try {
      await createMutation.mutateAsync(createData);
      setIsCreateDialogOpen(false);
      refetch();
    } catch (error) {
      console.error('Failed to create configuration:', error);
    }
  };

  const handleEditSubmit = async (formData: FormData) => {
    if (!editingConfig) return;

    const value = formData.get('value') as string;
    const description = formData.get('description') as string;
    const isPublic = formData.get('isPublic') === 'on';

    let parsedValue: any = value;
    try {
      if (editingConfig.type === 'number') {
        parsedValue = parseFloat(value);
      } else if (editingConfig.type === 'boolean') {
        parsedValue = value === 'true';
      } else if (editingConfig.type === 'object') {
        parsedValue = JSON.parse(value);
      }
    } catch (error) {
      console.error('Failed to parse value:', error);
      return;
    }

    const updateData = {
      value: parsedValue,
      description: description || undefined,
      isPublic,
    };

    try {
      await updateMutation.mutateAsync(updateData);
      setIsEditDialogOpen(false);
      setEditingConfig(null);
      refetch();
    } catch (error) {
      console.error('Failed to update configuration:', error);
    }
  };

  const handleDelete = async (key: string) => {
    if (window.confirm(`Are you sure you want to delete configuration "${key}"? This action cannot be undone.`)) {
      try {
        await deleteMutation.mutateAsync(key);
        refetch();
      } catch (error) {
        console.error('Failed to delete configuration:', error);
      }
    }
  };

  const openEditDialog = (config: SystemConfig) => {
    setEditingConfig(config);
    setIsEditDialogOpen(true);
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'string': return 'bg-blue-100 text-blue-800';
      case 'number': return 'bg-green-100 text-green-800';
      case 'boolean': return 'bg-purple-100 text-purple-800';
      case 'object': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'site': return <Globe className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      case 'features': return <Zap className="h-4 w-4" />;
      case 'api': return <Database className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const formatValue = (value: any, type: string) => {
    if (type === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load system configurations. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Configuration</h1>
          <p className="text-gray-600 mt-1">Manage system settings and parameters</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-nutrisnap-teal hover:bg-nutrisnap-teal/90">
              <Plus className="h-4 w-4 mr-2" />
              Add Configuration
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Configuration</DialogTitle>
            </DialogHeader>
            <form onSubmit={(e) => {
              e.preventDefault();
              handleCreateSubmit(new FormData(e.currentTarget));
            }} className="space-y-4">
              <div>
                <Label htmlFor="key">Key</Label>
                <Input id="key" name="key" placeholder="SETTING_NAME" required />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="type">Type</Label>
                  <Select name="type" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="string">String</SelectItem>
                      <SelectItem value="number">Number</SelectItem>
                      <SelectItem value="boolean">Boolean</SelectItem>
                      <SelectItem value="object">Object</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select name="category" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="site">Site</SelectItem>
                      <SelectItem value="security">Security</SelectItem>
                      <SelectItem value="features">Features</SelectItem>
                      <SelectItem value="api">API</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="value">Value</Label>
                <Textarea id="value" name="value" placeholder="Configuration value" required />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea id="description" name="description" placeholder="What this setting controls" />
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="isPublic" name="isPublic" />
                <Label htmlFor="isPublic">Public (accessible without authentication)</Label>
              </div>
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={createMutation.isPending}>
                  {createMutation.isPending ? 'Creating...' : 'Create'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search configurations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="category">Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All categories</SelectItem>
                  <SelectItem value="site">Site</SelectItem>
                  <SelectItem value="security">Security</SelectItem>
                  <SelectItem value="features">Features</SelectItem>
                  <SelectItem value="api">API</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button variant="outline" onClick={() => {
                setSearchTerm('');
                setCategoryFilter('all');
              }}>
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configurations by Category */}
      {isLoading ? (
        <div className="space-y-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[...Array(3)].map((_, j) => (
                    <div key={j} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <Skeleton className="h-4 w-48 mb-2" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                      <div className="flex space-x-2">
                        <Skeleton className="h-6 w-16" />
                        <Skeleton className="h-8 w-16" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : categories.length > 0 ? (
        <Tabs defaultValue={categories[0]} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            {categories.slice(0, 5).map((category) => (
              <TabsTrigger key={category} value={category} className="flex items-center space-x-2">
                {getCategoryIcon(category)}
                <span className="capitalize">{category}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {categories.map((category) => (
            <TabsContent key={category} value={category}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {getCategoryIcon(category)}
                    <span className="capitalize">{category} Settings</span>
                    <Badge variant="outline">{configsByCategory[category].length}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {configsByCategory[category].map((config) => (
                      <div key={config.key} className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="font-semibold">{config.key}</h3>
                            <Badge className={getTypeColor(config.type)}>
                              {config.type}
                            </Badge>
                            {config.isPublic ? (
                              <Badge variant="outline" className="flex items-center space-x-1">
                                <Eye className="h-3 w-3" />
                                <span>Public</span>
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="flex items-center space-x-1">
                                <EyeOff className="h-3 w-3" />
                                <span>Private</span>
                              </Badge>
                            )}
                          </div>
                          {config.description && (
                            <p className="text-sm text-gray-600 mb-2">{config.description}</p>
                          )}
                          <div className="bg-gray-100 p-2 rounded text-sm font-mono">
                            {config.type === 'object' ? (
                              <pre className="whitespace-pre-wrap">{formatValue(config.value, config.type)}</pre>
                            ) : (
                              formatValue(config.value, config.type)
                            )}
                          </div>
                          {config.updatedBy && (
                            <p className="text-xs text-gray-500 mt-2">
                              Updated by {config.updatedBy.name} on {format(new Date(config.updatedAt), 'MMM dd, yyyy HH:mm')}
                            </p>
                          )}
                        </div>
                        <div className="flex space-x-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(config)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(config.key)}
                            disabled={deleteMutation.isPending}
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      ) : (
        <div className="text-center py-12">
          <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No configurations found</h3>
          <p className="text-gray-600 mb-4">Get started by adding your first system configuration.</p>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Configuration
          </Button>
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Configuration</DialogTitle>
          </DialogHeader>
          {editingConfig && (
            <form onSubmit={(e) => {
              e.preventDefault();
              handleEditSubmit(new FormData(e.currentTarget));
            }} className="space-y-4">
              <div>
                <Label>Key</Label>
                <Input value={editingConfig.key} disabled />
              </div>
              <div>
                <Label>Type</Label>
                <Input value={editingConfig.type} disabled />
              </div>
              <div>
                <Label htmlFor="edit-value">Value</Label>
                <Textarea 
                  id="edit-value" 
                  name="value" 
                  defaultValue={formatValue(editingConfig.value, editingConfig.type)}
                  rows={editingConfig.type === 'object' ? 6 : 3}
                  required 
                />
              </div>
              <div>
                <Label htmlFor="edit-description">Description</Label>
                <Textarea 
                  id="edit-description" 
                  name="description" 
                  defaultValue={editingConfig.description || ''}
                  placeholder="What this setting controls" 
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch 
                  id="edit-isPublic" 
                  name="isPublic" 
                  defaultChecked={editingConfig.isPublic} 
                />
                <Label htmlFor="edit-isPublic">Public (accessible without authentication)</Label>
              </div>
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => {
                  setIsEditDialogOpen(false);
                  setEditingConfig(null);
                }}>
                  Cancel
                </Button>
                <Button type="submit" disabled={updateMutation.isPending}>
                  {updateMutation.isPending ? 'Updating...' : 'Update'}
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SystemConfiguration;
