import { useState, useCallback } from 'react';
import FoodAnalysisService, { AIAnalysisResponse } from '@/services/food-analysis.service';
import { toast } from 'sonner';

export interface AIAnalysisState {
  isAnalyzing: boolean;
  analysisResult: AIAnalysisResponse | null;
  error: string | null;
}

export interface ProcessedFoodItem {
  name: string;
  portionSize: string;
  calories: number;
  protein: number;
  carbohydrates: number;
  fats: number;
  confidence: number;
  notes?: string;
  userVerified: boolean;
  userAdded?: boolean;
}

export interface ProcessedNutritionalSummary {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  sugar: number;
  sodium: number;
}

export const useAIAnalysis = () => {
  const [state, setState] = useState<AIAnalysisState>({
    isAnalyzing: false,
    analysisResult: null,
    error: null,
  });

  const analyzeImage = useCallback(async (image: File) => {
    setState(prev => ({
      ...prev,
      isAnalyzing: true,
      error: null,
      analysisResult: null,
    }));

    try {
      console.log('🔍 Starting AI analysis for image:', image.name);
      
      const result = await FoodAnalysisService.analyzeImage(image);
      
      if (result.success && result.data) {
        console.log('✅ AI analysis completed successfully');
        setState(prev => ({
          ...prev,
          isAnalyzing: false,
          analysisResult: result,
        }));
        
        toast.success('Food analysis completed successfully!');
        return result;
      } else {
        const errorMessage = result.message || 'Analysis failed';
        console.error('❌ AI analysis failed:', errorMessage);
        
        setState(prev => ({
          ...prev,
          isAnalyzing: false,
          error: errorMessage,
        }));
        
        // Show specific error messages
        if (result.error === 'NOT_FOOD') {
          toast.error('No food detected in the image. Please upload an image containing food.');
        } else if (result.error === 'API_ERROR') {
          toast.error('AI analysis service is temporarily unavailable. Please try again later.');
        } else if (result.error === 'TIMEOUT') {
          toast.error('Analysis timed out. Please try again with a smaller image.');
        } else {
          toast.error(errorMessage);
        }
        
        return null;
      }
    } catch (error: any) {
      console.error('❌ AI analysis error:', error);

      let errorMessage = 'An unexpected error occurred during analysis';

      // Handle specific backend error responses
      if (error.response?.status === 400) {
        const backendError = error.response.data?.error;
        const backendMessage = error.response.data?.message;

        if (backendError === 'NOT_FOOD') {
          errorMessage = 'The uploaded image does not appear to contain food items. Please upload a clear photo of food.';
        } else if (backendMessage?.includes('rate limit')) {
          errorMessage = 'Too many analysis requests. Please wait 15 minutes before trying again.';
        } else if (backendMessage?.includes('file size')) {
          errorMessage = 'File too large. Maximum size is 10MB.';
        } else if (backendMessage?.includes('format')) {
          errorMessage = 'Unsupported file format. Please use JPEG, PNG, WebP, or AVIF.';
        } else {
          errorMessage = backendMessage || 'Invalid image or request. Please try again.';
        }
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication required. Please log in again.';
      } else if (error.response?.status === 429) {
        errorMessage = 'Too many requests. Please wait 15 minutes before trying again.';
      } else if (error.response?.status === 413) {
        errorMessage = 'File too large. Maximum size is 10MB.';
      } else if (error.response?.status === 415) {
        errorMessage = 'Unsupported file format. Please use JPEG, PNG, WebP, or AVIF.';
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = 'Analysis timed out. Please try again with a smaller image.';
      } else if (error.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setState(prev => ({
        ...prev,
        isAnalyzing: false,
        error: errorMessage,
      }));

      toast.error(errorMessage);
      return null;
    }
  }, []);

  const processAnalysisResult = useCallback((result: AIAnalysisResponse): {
    foodItems: ProcessedFoodItem[];
    nutritionalSummary: ProcessedNutritionalSummary;
  } | null => {
    if (!result.success || !result.data?.analysis) {
      return null;
    }

    const analysis = result.data.analysis;

    // Process food items
    const foodItems: ProcessedFoodItem[] = analysis.food_items.map(item => ({
      name: item.name,
      portionSize: item.portion_size,
      calories: parseFloat(item.calories.replace(/[^\d.]/g, '')) || 0,
      protein: parseFloat(item.protein.replace(/[^\d.]/g, '')) || 0,
      carbohydrates: parseFloat(item.carbohydrates.replace(/[^\d.]/g, '')) || 0,
      fats: parseFloat(item.fats.replace(/[^\d.]/g, '')) || 0,
      confidence: parseFloat(item.confidence) || 0,
      notes: item.notes,
      userVerified: false,
      userAdded: false,
    }));

    // Process nutritional totals
    const totals = analysis.totals;

    // Calculate base nutritional values first
    const baseCalories = parseFloat(totals.total_calories.replace(/[^\d.]/g, '')) || 0;
    const baseProtein = parseFloat(totals.total_protein.replace(/[^\d.]/g, '')) || 0;
    const baseCarbs = parseFloat(totals.total_carbohydrates.replace(/[^\d.]/g, '')) || 0;
    const baseFat = parseFloat(totals.total_fats.replace(/[^\d.]/g, '')) || 0;

    // Now create the nutritional summary with all values properly calculated
    const nutritionalSummary: ProcessedNutritionalSummary = {
      calories: baseCalories,
      protein: baseProtein,
      carbs: baseCarbs,
      fat: baseFat,
      // These are estimates since Gemini doesn't provide them
      fiber: Math.round((foodItems.length * 2.5)), // Rough estimate: 2.5g per food item
      sugar: Math.round(baseCarbs * 0.1), // Rough estimate: 10% of carbs
      sodium: Math.round((foodItems.length * 150)), // Rough estimate: 150mg per food item
    };

    return {
      foodItems,
      nutritionalSummary,
    };
  }, []);

  const resetAnalysis = useCallback(() => {
    setState({
      isAnalyzing: false,
      analysisResult: null,
      error: null,
    });
  }, []);

  const retryAnalysis = useCallback(async (image: File) => {
    console.log('🔄 Retrying AI analysis...');
    return await analyzeImage(image);
  }, [analyzeImage]);

  return {
    ...state,
    analyzeImage,
    processAnalysisResult,
    resetAnalysis,
    retryAnalysis,
  };
};
