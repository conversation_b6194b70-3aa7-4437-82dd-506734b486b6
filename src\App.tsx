
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import Index from "./pages/Index";
import Dashboard from "./pages/dashboard";
import NotFound from "./pages/NotFound";
import SnapNew from "./pages/SnapNew";
import MealHistory from "./pages/meal-history";
import MealDetailsPage from "./pages/MealDetailsPage";
import NutritionSummary from "./pages/NutritionSummary";
import StatusTrends from "./pages/status-trends/StatusTrends";
import Settings from "./pages/settings";
import Gallery from "./pages/Gallery";
import Announcements from "./pages/Announcements";
import RequireAuth from "./components/auth/RequireAuth";
import RequireRole from "./components/auth/RequireRole";
import DashboardLayout from "./components/layout/DashboardLayout";
import AdminDashboard from "./pages/admin/AdminDashboard";
import GalleryManagement from "./pages/admin/GalleryManagement";
import AnnouncementsManagement from "./pages/admin/AnnouncementsManagement";
import UserManagement from "./pages/admin/UserManagement";
import SystemConfiguration from "./pages/admin/SystemConfiguration";
import SystemLogs from "./pages/admin/SystemLogs";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />

            {/* Public routes */}
            <Route path="/gallery" element={<Gallery />} />
            <Route path="/announcements" element={<Announcements />} />

            {/* Protected routes with dashboard layout - Regular users only */}
            <Route path="/dashboard" element={
              <RequireAuth>
                <RequireRole role="user" allowRegularUsers={true}>
                  <DashboardLayout>
                    <Dashboard />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/snap-new" element={
              <RequireAuth>
                <RequireRole role="user" allowRegularUsers={true}>
                  <DashboardLayout>
                    <SnapNew />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/meal-history" element={
              <RequireAuth>
                <RequireRole role="user" allowRegularUsers={true}>
                  <DashboardLayout>
                    <MealHistory />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/meal-details/:id" element={
              <RequireAuth>
                <RequireRole role="user" allowRegularUsers={true}>
                  <DashboardLayout>
                    <MealDetailsPage />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/nutrition-summary" element={
              <RequireAuth>
                <RequireRole role="user" allowRegularUsers={true}>
                  <DashboardLayout>
                    <NutritionSummary />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/status-trends" element={
              <RequireAuth>
                <RequireRole role="user" allowRegularUsers={true}>
                  <DashboardLayout>
                    <StatusTrends />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/settings" element={
              <RequireAuth>
                <RequireRole role="user" allowRegularUsers={true}>
                  <DashboardLayout>
                    <Settings />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            
            {/* Admin routes */}
            <Route path="/admin/dashboard" element={
              <RequireAuth>
                <RequireRole role="editor">
                  <DashboardLayout>
                    <AdminDashboard />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/admin/gallery" element={
              <RequireAuth>
                <RequireRole role="editor">
                  <DashboardLayout>
                    <GalleryManagement />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/admin/announcements" element={
              <RequireAuth>
                <RequireRole role="editor">
                  <DashboardLayout>
                    <AnnouncementsManagement />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/admin/users" element={
              <RequireAuth>
                <RequireRole role="admin">
                  <DashboardLayout>
                    <UserManagement />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/admin/config" element={
              <RequireAuth>
                <RequireRole role="admin">
                  <DashboardLayout>
                    <SystemConfiguration />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            <Route path="/admin/logs" element={
              <RequireAuth>
                <RequireRole role="admin">
                  <DashboardLayout>
                    <SystemLogs />
                  </DashboardLayout>
                </RequireRole>
              </RequireAuth>
            } />
            
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
