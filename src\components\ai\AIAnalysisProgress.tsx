import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Eye, 
  Calculator, 
  CheckCircle,
  Loader2
} from "lucide-react";

interface AIAnalysisProgressProps {
  isAnalyzing: boolean;
  onComplete?: () => void;
}

const analysisSteps = [
  {
    id: 'upload',
    label: 'Image Upload',
    description: 'Uploading and validating image',
    icon: <Eye className="h-4 w-4" />,
    duration: 1000,
  },
  {
    id: 'detection',
    label: 'Food Detection',
    description: 'AI identifying food items in image',
    icon: <Brain className="h-4 w-4" />,
    duration: 3000,
  },
  {
    id: 'analysis',
    label: 'Nutritional Analysis',
    description: 'Calculating nutritional information',
    icon: <Calculator className="h-4 w-4" />,
    duration: 2000,
  },
  {
    id: 'complete',
    label: 'Analysis Complete',
    description: 'Results ready for review',
    icon: <CheckCircle className="h-4 w-4" />,
    duration: 500,
  },
];

const AIAnalysisProgress: React.FC<AIAnalysisProgressProps> = ({
  isAnalyzing,
  onComplete,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [stepProgress, setStepProgress] = useState(0);

  useEffect(() => {
    if (!isAnalyzing) {
      setCurrentStep(0);
      setProgress(0);
      setStepProgress(0);
      return;
    }

    let stepIndex = 0;
    let totalElapsed = 0;
    const totalDuration = analysisSteps.reduce((sum, step) => sum + step.duration, 0);

    const runStep = () => {
      if (stepIndex >= analysisSteps.length) {
        setProgress(100);
        setCurrentStep(analysisSteps.length - 1);
        setStepProgress(100);
        onComplete?.();
        return;
      }

      const step = analysisSteps[stepIndex];
      setCurrentStep(stepIndex);
      
      let stepElapsed = 0;
      const stepInterval = setInterval(() => {
        stepElapsed += 100;
        const stepProgressPercent = Math.min((stepElapsed / step.duration) * 100, 100);
        const overallProgressPercent = Math.min(((totalElapsed + stepElapsed) / totalDuration) * 100, 100);
        
        setStepProgress(stepProgressPercent);
        setProgress(overallProgressPercent);

        if (stepElapsed >= step.duration) {
          clearInterval(stepInterval);
          totalElapsed += step.duration;
          stepIndex++;
          setTimeout(runStep, 200); // Small delay between steps
        }
      }, 100);
    };

    runStep();
  }, [isAnalyzing, onComplete]);

  if (!isAnalyzing) {
    return null;
  }

  const currentStepData = analysisSteps[currentStep];

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center text-lg">
          <Loader2 className="h-5 w-5 mr-2 animate-spin text-nutrisnap-teal" />
          AI Food Analysis
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="font-medium">Overall Progress</span>
            <span className="text-nutrisnap-teal">{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Current Step */}
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-nutrisnap-teal/10 rounded-full flex items-center justify-center">
              {currentStepData.icon}
            </div>
            <div className="flex-1">
              <p className="font-medium text-sm">{currentStepData.label}</p>
              <p className="text-xs text-gray-600">{currentStepData.description}</p>
            </div>
            <Badge variant="secondary" className="text-xs">
              {currentStep + 1}/{analysisSteps.length}
            </Badge>
          </div>

          {/* Step Progress */}
          <div className="space-y-1">
            <Progress value={stepProgress} className="h-1" />
          </div>
        </div>

        {/* Steps Overview */}
        <div className="space-y-2">
          {analysisSteps.map((step, index) => (
            <div
              key={step.id}
              className={`flex items-center space-x-2 text-xs ${
                index < currentStep
                  ? 'text-green-600'
                  : index === currentStep
                  ? 'text-nutrisnap-teal'
                  : 'text-gray-400'
              }`}
            >
              <div className="w-4 h-4 flex items-center justify-center">
                {index < currentStep ? (
                  <CheckCircle className="h-3 w-3" />
                ) : index === currentStep ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <div className="w-2 h-2 rounded-full bg-gray-300" />
                )}
              </div>
              <span>{step.label}</span>
            </div>
          ))}
        </div>

        {/* Tips */}
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <p className="text-xs text-blue-700">
            <strong>Tip:</strong> For best results, ensure your image shows food items clearly 
            with good lighting and minimal background clutter.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default AIAnalysisProgress;
