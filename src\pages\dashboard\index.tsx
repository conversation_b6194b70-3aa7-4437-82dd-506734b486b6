
import React, { useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useFoodAnalyses } from "@/hooks/use-food-analysis";
import { format } from "date-fns";
import { useNavigate, useLocation } from "react-router-dom";

import DashboardHeader from "./components/DashboardHeader";
import QuickActions from "./components/QuickActions";
import TodayNutrition from "./components/TodayNutrition";
import RecentMeals from "./components/RecentMeals";
import MealBreakdown from "./components/MealBreakdown";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

const Dashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect admin and editor users to admin dashboard only on first login
  useEffect(() => {
    if (user && (user.role === 'admin' || user.role === 'editor') && location.pathname === '/dashboard') {
      // Only redirect if they're coming from login (not navigating back intentionally)
      const isFromLogin = !document.referrer || document.referrer.includes('/');
      if (isFromLogin) {
        navigate('/admin/dashboard', { replace: true });
      }
    }
  }, [user, navigate, location.pathname]);

  // Get today's date in ISO format (YYYY-MM-DD)
  const today = format(new Date(), 'yyyy-MM-dd');

  // Fetch food analyses with date filter for today
  const {
    data,
    isLoading,
    error
  } = useFoodAnalyses({
    startDate: `${today}T00:00:00Z`,
    endDate: `${today}T23:59:59Z`
  });

  // Get all food analyses for recent meals and meal breakdown
  const {
    data: allAnalysesData,
    isLoading: isLoadingAll
  } = useFoodAnalyses();

  // Use the fetched data or empty array if loading/error
  const todayMeals = data?.foodAnalyses || [];
  const recentMeals = allAnalysesData?.foodAnalyses || [];

  // Calculate today's nutrition from real data
  const todayNutrition = todayMeals.reduce(
    (acc, meal) => {
      acc.calories += meal.nutritionalSummary.calories;
      acc.protein += meal.nutritionalSummary.protein;
      acc.carbs += meal.nutritionalSummary.carbs;
      acc.fat += meal.nutritionalSummary.fat;
      return acc;
    },
    { calories: 0, protein: 0, carbs: 0, fat: 0 }
  );

  // Calculate nutritional goals based on user preferences
  const nutritionGoals = user?.preferences.nutritionGoals || {
    calories: 2000,
    protein: 120,
    carbs: 250,
    fat: 70,
  };

  // Calculate meal breakdown from all meals
  const mealCountByCategory = recentMeals.reduce((acc, meal) => {
    acc[meal.mealCategory] = (acc[meal.mealCategory] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Handle error state
  if (error) {
    return (
      <div className="flex min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto w-full">
          <DashboardHeader />
          <QuickActions />
          <Alert variant="destructive" className="my-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to load your nutrition data. Please try again later.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          <DashboardHeader />
          <QuickActions />

          {isLoading ? (
            <div className="space-y-4 my-8">
              <Skeleton className="h-8 w-1/3" />
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Skeleton className="h-24 rounded-lg" />
                <Skeleton className="h-24 rounded-lg" />
                <Skeleton className="h-24 rounded-lg" />
                <Skeleton className="h-24 rounded-lg" />
              </div>
            </div>
          ) : (
            <TodayNutrition
              todayNutrition={todayNutrition}
              nutritionGoals={nutritionGoals}
            />
          )}

          {isLoadingAll ? (
            <div className="space-y-4 my-8">
              <Skeleton className="h-8 w-1/3" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Skeleton className="h-32 rounded-lg" />
                <Skeleton className="h-32 rounded-lg" />
              </div>
            </div>
          ) : (
            <>
              <RecentMeals meals={recentMeals.slice(0, 6)} />
              <MealBreakdown mealCountByCategory={mealCountByCategory} />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
