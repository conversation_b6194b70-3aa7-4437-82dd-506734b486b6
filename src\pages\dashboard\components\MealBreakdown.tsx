
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar } from "lucide-react";
import { mockFoodAnalyses } from "@/data/mockData";

type MealBreakdownProps = {
  mealCountByCategory: Record<string, number>;
};

const MealBreakdown: React.FC<MealBreakdownProps> = ({ mealCountByCategory }) => {
  return (
    <div>
      <h2 className="text-xl font-bold text-nutrisnap-charcoal mb-4">
        Meal Breakdown
      </h2>
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex flex-col items-center justify-center p-4 bg-yellow-50 rounded-lg">
              <div className="bg-yellow-200 p-2 rounded-full mb-3">
                <Calendar className="h-5 w-5 text-yellow-600" />
              </div>
              <span className="text-xl font-bold">
                {mealCountByCategory.breakfast || 0}
              </span>
              <span className="text-sm text-gray-500">Breakfast</span>
            </div>
            <div className="flex flex-col items-center justify-center p-4 bg-green-50 rounded-lg">
              <div className="bg-green-200 p-2 rounded-full mb-3">
                <Calendar className="h-5 w-5 text-green-600" />
              </div>
              <span className="text-xl font-bold">
                {mealCountByCategory.lunch || 0}
              </span>
              <span className="text-sm text-gray-500">Lunch</span>
            </div>
            <div className="flex flex-col items-center justify-center p-4 bg-blue-50 rounded-lg">
              <div className="bg-blue-200 p-2 rounded-full mb-3">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
              <span className="text-xl font-bold">
                {mealCountByCategory.dinner || 0}
              </span>
              <span className="text-sm text-gray-500">Dinner</span>
            </div>
            <div className="flex flex-col items-center justify-center p-4 bg-purple-50 rounded-lg">
              <div className="bg-purple-200 p-2 rounded-full mb-3">
                <Calendar className="h-5 w-5 text-purple-600" />
              </div>
              <span className="text-xl font-bold">
                {mealCountByCategory.snack || 0}
              </span>
              <span className="text-sm text-gray-500">Snacks</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MealBreakdown;
