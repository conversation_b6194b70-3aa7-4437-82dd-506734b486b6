
import React from "react";
import { formatDistanceToNow } from "date-fns";
import { Calendar, Clock, MessageSquare, Tag } from "lucide-react";
import { getImageUrl } from "@/lib/image-utils";
import { formatCalories, formatDisplayNumber } from "@/utils/numberUtils";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface FoodItem {
  foodItem: string;
  confidence: number;
  boundingBox: { x: number; y: number; width: number; height: number };
  quantityGrams: number;
  commonPortions: string[];
  selectedPortion: string;
  userVerified: boolean;
  userAdded?: boolean;
}

interface MealDetailProps {
  isOpen: boolean;
  onClose: () => void;
  meal: {
    id: string;
    imageUrl: string;
    userNotes: string;
    mealCategory: string;
    mealDateTime: string;
    recognitionResults: FoodItem[];
    nutritionalSummary: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
      fiber: number;
      sugar: number;
      sodium: number;
    };
  } | null;
}

const MealDetail: React.FC<MealDetailProps> = ({ isOpen, onClose, meal }) => {
  if (!meal) return null;

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });
  };

  const getMealCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case "breakfast":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "lunch":
        return "bg-green-100 text-green-800 border-green-200";
      case "dinner":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "snack":
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatNutritionValue = (value: number, isCalories: boolean = false) => {
    return isCalories ? formatCalories(value) : formatDisplayNumber(value);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">Meal Details</DialogTitle>
          <DialogDescription>
            View detailed information about this meal.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Image and basic info */}
          <div className="relative rounded-md overflow-hidden h-64">
            <img
              src={getImageUrl(meal.imageUrl)}
              alt={meal.userNotes}
              className="w-full h-full object-cover"
            />
            <Badge
              className={`absolute top-3 right-3 ${getMealCategoryColor(
                meal.mealCategory
              )}`}
            >
              {meal.mealCategory.charAt(0).toUpperCase() +
                meal.mealCategory.slice(1)}
            </Badge>
          </div>

          {/* Meal Info */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">{meal.userNotes}</h3>

            <div className="flex flex-col space-y-2">
              <div className="flex items-center text-gray-600">
                <Calendar className="h-4 w-4 mr-2" />
                {formatDate(meal.mealDateTime)}
              </div>
              <div className="flex items-center text-gray-600">
                <Clock className="h-4 w-4 mr-2" />
                {formatTime(meal.mealDateTime)} ({getTimeAgo(meal.mealDateTime)})
              </div>
              {meal.userNotes && (
                <div className="flex items-start text-gray-600">
                  <MessageSquare className="h-4 w-4 mr-2 mt-0.5" />
                  {meal.userNotes}
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Nutritional Summary */}
          <div>
            <h4 className="text-lg font-semibold mb-3">Nutritional Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="bg-green-50 p-3 rounded-md">
                <div className="text-sm text-gray-600">Calories</div>
                <div className="text-xl font-semibold">
                  {formatNutritionValue(meal.nutritionalSummary.calories, true)}
                </div>
              </div>
              <div className="bg-blue-50 p-3 rounded-md">
                <div className="text-sm text-gray-600">Protein</div>
                <div className="text-xl font-semibold">
                  {formatNutritionValue(meal.nutritionalSummary.protein)}g
                </div>
              </div>
              <div className="bg-yellow-50 p-3 rounded-md">
                <div className="text-sm text-gray-600">Carbs</div>
                <div className="text-xl font-semibold">
                  {formatNutritionValue(meal.nutritionalSummary.carbs)}g
                </div>
              </div>
              <div className="bg-red-50 p-3 rounded-md">
                <div className="text-sm text-gray-600">Fat</div>
                <div className="text-xl font-semibold">
                  {formatNutritionValue(meal.nutritionalSummary.fat)}g
                </div>
              </div>
              <div className="bg-purple-50 p-3 rounded-md">
                <div className="text-sm text-gray-600">Fiber</div>
                <div className="text-xl font-semibold">
                  {formatNutritionValue(meal.nutritionalSummary.fiber)}g
                </div>
              </div>
              <div className="bg-orange-50 p-3 rounded-md">
                <div className="text-sm text-gray-600">Sugar</div>
                <div className="text-xl font-semibold">
                  {formatNutritionValue(meal.nutritionalSummary.sugar)}g
                </div>
              </div>
              <div className="bg-gray-50 p-3 rounded-md">
                <div className="text-sm text-gray-600">Sodium</div>
                <div className="text-xl font-semibold">
                  {formatNutritionValue(meal.nutritionalSummary.sodium)}mg
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Food Items */}
          <div>
            <h4 className="text-lg font-semibold mb-3">Food Items</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item</TableHead>
                  <TableHead className="text-right">Portion</TableHead>
                  <TableHead className="text-right">Quantity (g)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {meal.recognitionResults.map((food, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <Tag className="h-4 w-4 mr-2 text-gray-500" />
                        {food.foodItem}
                        {food.userVerified && (
                          <Badge className="ml-2 bg-green-100 text-green-800 border-green-200">
                            Verified
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {food.selectedPortion}
                    </TableCell>
                    <TableCell className="text-right">
                      {food.quantityGrams}g
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MealDetail;
