
export interface SettingsSectionProps {
  formData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    confirmPassword: string;
    dietaryRestrictions: string[];
    nutritionGoals: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
    };
    units: "metric" | "imperial";
    notifications: {
      mealReminders: boolean;
      weeklyReports: boolean;
      achievementAlerts: boolean;
    };
  };
  setFormData: React.Dispatch<React.SetStateAction<any>>;
}
