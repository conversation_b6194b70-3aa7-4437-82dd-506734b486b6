
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Activity, Utensils, Calendar, BarChart } from "lucide-react";

interface SummaryStatsProps {
  summary: {
    avgCalories: number;
    avgProtein: number;
    avgCarbs: number;
    avgFat: number;
    trackingRate: number;
  };
  totalAnalyses: number;
}

const SummaryStats: React.FC<SummaryStatsProps> = ({ summary, totalAnalyses }) => {
  const statCards = [
    {
      label: "Average Calories",
      value: summary.avgCalories,
      suffix: "",
      description: "per day tracked",
      icon: <Activity className="w-5 h-5 text-nutrisnap-teal" />,
      bgColor: "from-nutrisnap-teal/10 to-white"
    },
    {
      label: "Average Protein",
      value: summary.avgProtein,
      suffix: "g",
      description: "per day tracked",
      icon: <Utensils className="w-5 h-5 text-nutrisnap-orange" />,
      bgColor: "from-nutrisnap-orange/10 to-white"
    },
    {
      label: "Tracking Rate",
      value: summary.trackingRate,
      suffix: "%",
      description: "of days tracked",
      icon: <Calendar className="w-5 h-5 text-nutrisnap-purple" />,
      bgColor: "from-nutrisnap-purple/10 to-white"
    },
    {
      label: "Total Analyses",
      value: totalAnalyses,
      suffix: "",
      description: "meals analyzed",
      icon: <BarChart className="w-5 h-5 text-nutrisnap-charcoal" />,
      bgColor: "from-nutrisnap-charcoal/10 to-white"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      {statCards.map((stat, index) => (
        <Card key={index} className="overflow-hidden shadow-sm border-none hover:shadow-md transition-shadow duration-300">
          <CardContent className={`pt-6 bg-gradient-to-br ${stat.bgColor}`}>
            <div className="flex items-start justify-between mb-2">
              <p className="text-sm text-gray-600 font-medium">{stat.label}</p>
              <span className="rounded-full bg-white p-1.5 shadow-sm">
                {stat.icon}
              </span>
            </div>
            <div>
              <p className="text-3xl font-bold text-nutrisnap-charcoal">
                {stat.value}{stat.suffix}
              </p>
              <p className="text-xs text-gray-500">{stat.description}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default SummaryStats;
