
import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { dietaryRestrictions } from "@/data/mockData";
import { SettingsSectionProps } from "../types";

const DietaryPreferencesSection: React.FC<SettingsSectionProps> = ({ formData, setFormData }) => {
  const handleToggleRestriction = (restriction: string) => {
    const restrictions = [...formData.dietaryRestrictions];
    const index = restrictions.indexOf(restriction);
    
    if (index === -1) {
      restrictions.push(restriction);
    } else {
      restrictions.splice(index, 1);
    }
    
    setFormData({
      ...formData,
      dietaryRestrictions: restrictions,
    });
  };

  const handleNutritionGoalChange = (
    field: keyof typeof formData.nutritionGoals,
    value: string
  ) => {
    const numValue = parseInt(value, 10) || 0;
    setFormData({
      ...formData,
      nutritionGoals: {
        ...formData.nutritionGoals,
        [field]: numValue,
      },
    });
  };

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle>Dietary Preferences</CardTitle>
        <CardDescription>Set your dietary restrictions and preferences</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label className="mb-2 block">Dietary Restrictions</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {dietaryRestrictions.map((restriction) => (
                <div
                  key={restriction}
                  className="flex items-center space-x-2"
                >
                  <input
                    type="checkbox"
                    id={restriction}
                    checked={formData.dietaryRestrictions.includes(restriction)}
                    onChange={() => handleToggleRestriction(restriction)}
                    className="rounded text-nutrisnap-teal focus:ring-nutrisnap-teal"
                  />
                  <Label
                    htmlFor={restriction}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {restriction.charAt(0).toUpperCase() + restriction.slice(1)}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <Label className="mb-4 block">Nutrition Goals</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="calorieGoal"
                  className="text-sm text-gray-500"
                >
                  Daily Calorie Goal
                </Label>
                <Input
                  id="calorieGoal"
                  type="number"
                  min="0"
                  value={formData.nutritionGoals.calories}
                  onChange={(e) =>
                    handleNutritionGoalChange("calories", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="proteinGoal"
                  className="text-sm text-gray-500"
                >
                  Daily Protein Goal (g)
                </Label>
                <Input
                  id="proteinGoal"
                  type="number"
                  min="0"
                  value={formData.nutritionGoals.protein}
                  onChange={(e) =>
                    handleNutritionGoalChange("protein", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="carbGoal"
                  className="text-sm text-gray-500"
                >
                  Daily Carbs Goal (g)
                </Label>
                <Input
                  id="carbGoal"
                  type="number"
                  min="0"
                  value={formData.nutritionGoals.carbs}
                  onChange={(e) =>
                    handleNutritionGoalChange("carbs", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="fatGoal"
                  className="text-sm text-gray-500"
                >
                  Daily Fat Goal (g)
                </Label>
                <Input
                  id="fatGoal"
                  type="number"
                  min="0"
                  value={formData.nutritionGoals.fat}
                  onChange={(e) =>
                    handleNutritionGoalChange("fat", e.target.value)
                  }
                />
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <Label htmlFor="units" className="mb-2 block">
              Measurement Units
            </Label>
            <Select
              value={formData.units}
              onValueChange={(value) =>
                setFormData({
                  ...formData,
                  units: value as "metric" | "imperial",
                })
              }
            >
              <SelectTrigger id="units" className="w-full md:w-1/2">
                <SelectValue placeholder="Select units" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="metric">
                  Metric (grams, cm)
                </SelectItem>
                <SelectItem value="imperial">
                  Imperial (oz, inches)
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DietaryPreferencesSection;
