import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

interface RoleBasedRedirectProps {
  children: React.ReactNode;
}

const RoleBasedRedirect: React.FC<RoleBasedRedirectProps> = ({ children }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (user && location.pathname === '/dashboard') {
      // Redirect admins and editors to admin dashboard
      if (user.role === 'admin' || user.role === 'editor') {
        console.log('🔄 Redirecting admin/editor to admin dashboard');
        navigate('/admin/dashboard', { replace: true });
        return;
      }
    }
  }, [user, navigate, location.pathname]);

  // If user is admin/editor and trying to access /dashboard, show loading while redirecting
  if (user && (user.role === 'admin' || user.role === 'editor') && location.pathname === '/dashboard') {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nutrisnap-teal mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to admin dashboard...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default RoleBasedRedirect;
