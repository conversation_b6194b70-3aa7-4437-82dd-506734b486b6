import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { GoalData } from '../StatusTrends';
import { formatDisplayNumber, formatCalories } from '@/utils/numberUtils';
import { Target, Zap, Activity, Wheat, Droplets } from 'lucide-react';

interface GoalProgressProps {
  data: GoalData;
}

const GoalProgress: React.FC<GoalProgressProps> = ({ data }) => {
  // Goal configuration with icons and colors
  const goalConfig = {
    calories: {
      label: 'Calories',
      icon: Zap,
      color: 'bg-blue-500',
      unit: 'kcal',
      formatter: formatCalories,
    },
    protein: {
      label: 'Protein',
      icon: Activity,
      color: 'bg-red-500',
      unit: 'g',
      formatter: formatDisplayNumber,
    },
    carbs: {
      label: 'Carbohydrates',
      icon: Wheat,
      color: 'bg-green-500',
      unit: 'g',
      formatter: formatDisplayNumber,
    },
    fat: {
      label: 'Fat',
      icon: Droplets,
      color: 'bg-yellow-500',
      unit: 'g',
      formatter: formatDisplayNumber,
    },
  };

  // Calculate overall progress
  const overallProgress = Object.values(data).reduce(
    (sum, goal) => sum + Math.min(goal.percentage, 100),
    0
  ) / Object.keys(data).length;

  // Get progress color based on percentage
  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-green-500';
    if (percentage >= 70) return 'bg-yellow-500';
    if (percentage >= 50) return 'bg-orange-500';
    return 'bg-red-500';
  };

  // Get status message
  const getStatusMessage = (percentage: number) => {
    if (percentage >= 100) return 'Goal achieved! 🎉';
    if (percentage >= 90) return 'Almost there! 💪';
    if (percentage >= 70) return 'Good progress 👍';
    if (percentage >= 50) return 'Keep going! 🚀';
    return 'Let\'s get started! 💪';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          Goal Progress
        </CardTitle>
        
        {/* Overall Progress */}
        <div className="mt-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-sm text-gray-600">
              {formatDisplayNumber(overallProgress)}%
            </span>
          </div>
          <Progress 
            value={overallProgress} 
            className="h-3"
          />
          <p className="text-xs text-gray-600 mt-1">
            {getStatusMessage(overallProgress)}
          </p>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {Object.entries(data).map(([key, goal]) => {
          const config = goalConfig[key as keyof typeof goalConfig];
          const IconComponent = config.icon;
          const percentage = Math.min(goal.percentage, 100);
          const isOverTarget = goal.percentage > 100;

          return (
            <div key={key} className="space-y-3">
              {/* Goal Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`p-2 rounded-lg ${config.color} bg-opacity-10`}>
                    <IconComponent className={`h-4 w-4 text-${config.color.split('-')[1]}-600`} />
                  </div>
                  <span className="font-medium">{config.label}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold">
                    {config.formatter(goal.current)} / {config.formatter(goal.target)} {config.unit}
                  </p>
                  <p className={`text-xs ${
                    isOverTarget ? 'text-orange-600' : 
                    percentage >= 90 ? 'text-green-600' : 
                    percentage >= 70 ? 'text-yellow-600' : 
                    'text-gray-600'
                  }`}>
                    {formatDisplayNumber(goal.percentage)}%
                    {isOverTarget && ' (Over target)'}
                  </p>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-1">
                <Progress 
                  value={percentage} 
                  className="h-2"
                />
                
                {/* Progress Indicators */}
                <div className="flex justify-between text-xs text-gray-500">
                  <span>0</span>
                  <span className="text-center">50%</span>
                  <span>{config.formatter(goal.target)}</span>
                </div>
              </div>

              {/* Status Message */}
              <div className="flex items-center justify-between">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  percentage >= 100 ? 'bg-green-100 text-green-800' :
                  percentage >= 90 ? 'bg-yellow-100 text-yellow-800' :
                  percentage >= 70 ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {percentage >= 100 ? 'Complete' :
                   percentage >= 90 ? 'Almost there' :
                   percentage >= 70 ? 'Good progress' :
                   'In progress'}
                </span>
                
                {goal.current < goal.target && (
                  <span className="text-xs text-gray-600">
                    {config.formatter(goal.target - goal.current)} {config.unit} to go
                  </span>
                )}
              </div>
            </div>
          );
        })}

        {/* Motivational Message */}
        <div className="mt-6 p-4 bg-gradient-to-r from-nutrisnap-teal/10 to-blue-500/10 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Target className="h-4 w-4 text-nutrisnap-teal" />
            <span className="font-medium text-nutrisnap-teal">Daily Goals</span>
          </div>
          <p className="text-sm text-gray-700">
            {overallProgress >= 90 
              ? "Excellent work! You're crushing your nutrition goals! 🌟"
              : overallProgress >= 70
              ? "Great progress! Keep up the momentum! 💪"
              : overallProgress >= 50
              ? "You're on the right track! Stay consistent! 🚀"
              : "Every meal is a new opportunity to reach your goals! 🎯"
            }
          </p>
        </div>

        {/* Quick Tips */}
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Quick Tips:</h4>
          <ul className="text-xs text-gray-600 space-y-1">
            {data.protein.percentage < 80 && (
              <li>• Add more protein-rich foods like chicken, fish, or legumes</li>
            )}
            {data.calories.percentage < 70 && (
              <li>• Consider adding healthy snacks to meet your calorie goals</li>
            )}
            {data.carbs.percentage > 120 && (
              <li>• Try reducing refined carbs and focus on complex carbohydrates</li>
            )}
            {data.fat.percentage < 60 && (
              <li>• Include healthy fats like avocados, nuts, and olive oil</li>
            )}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default GoalProgress;
