
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Title,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import UploadZone from "@/components/upload/UploadZone";
import { mealCategories } from "@/data/mockData";
import { ArrowLeft, ArrowRight, Calendar, Check, AlertCircle } from "lucide-react";
import NutritionCard from "@/components/nutrition/NutritionCard";
import { useUploadFoodAnalysis } from "@/hooks/use-food-analysis";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";

const steps = [
  "Upload Image",
  "Analysis Results",
  "Nutritional Details",
  "Save to History",
];

const SnapNew = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedImage, setSelectedImage] = useState<{
    file: File | null;
    preview: string | null;
  }>({
    file: null,
    preview: null,
  });
  const [analysisResults, setAnalysisResults] = useState<any[]>([]);
  const [nutritionalSummary, setNutritionalSummary] = useState({
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    sodium: 0,
  });
  const [mealDetails, setMealDetails] = useState({
    category: "breakfast",
    dateTime: new Date().toISOString().slice(0, 16),
    notes: "",
  });
  const [loading, setLoading] = useState(false);
  const [fadeClass, setFadeClass] = useState("");

  const handleImageSelected = (file: File, previewUrl: string) => {
    setSelectedImage({
      file,
      preview: previewUrl,
    });

    // Simulate AI analysis after image is selected
    simulateAnalysis(previewUrl);
  };

  const simulateAnalysis = (imageUrl: string) => {
    setLoading(true);

    // Simulate delay for AI analysis
    setTimeout(() => {
      // Generate mock food recognition results
      const mockRecognitionResults = [
        {
          foodItem: "Avocado Toast",
          confidence: 0.92,
          boundingBox: { x: 100, y: 100, width: 300, height: 200 },
          quantityGrams: 120,
          commonPortions: ["1 slice", "2 slices"],
          selectedPortion: "1 slice",
          userVerified: false,
        },
        {
          foodItem: "Fried Egg",
          confidence: 0.89,
          boundingBox: { x: 250, y: 150, width: 100, height: 100 },
          quantityGrams: 50,
          commonPortions: ["1 egg", "2 eggs"],
          selectedPortion: "1 egg",
          userVerified: false,
        },
        {
          foodItem: "Cherry Tomatoes",
          confidence: 0.85,
          boundingBox: { x: 200, y: 180, width: 150, height: 50 },
          quantityGrams: 30,
          commonPortions: ["3 tomatoes", "5 tomatoes"],
          selectedPortion: "3 tomatoes",
          userVerified: false,
        },
      ];

      setAnalysisResults(mockRecognitionResults);

      // Calculate nutritional summary based on recognized foods
      const summary = {
        calories: 350,
        protein: 15,
        carbs: 30,
        fat: 20,
        fiber: 8,
        sugar: 3,
        sodium: 400,
      };

      setNutritionalSummary(summary);
      setLoading(false);

      // Wait a moment then move to the next step
      setTimeout(() => {
        goToNextStep();
      }, 1000);
    }, 2000);
  };

  const goToNextStep = () => {
    setFadeClass("opacity-0");
    setTimeout(() => {
      setCurrentStep((prev) => prev + 1);
      setFadeClass("opacity-100");
    }, 300);
  };

  const goToPreviousStep = () => {
    setFadeClass("opacity-0");
    setTimeout(() => {
      setCurrentStep((prev) => prev - 1);
      setFadeClass("opacity-100");
    }, 300);
  };

  const handleSaveMeal = () => {
    setLoading(true);

    // Simulate saving to database
    setTimeout(() => {
      setLoading(false);
      toast.success("Meal saved successfully!");
      navigate("/dashboard");
    }, 1500);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className={`transition-opacity duration-300 ${fadeClass}`}>
            <Card className="border-dashed">
              <CardHeader>
                <CardTitle>Upload Your Food Image</CardTitle>
              </CardHeader>
              <CardContent>
                <UploadZone onImageSelected={handleImageSelected} />
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => navigate("/dashboard")}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Dashboard
                </Button>
                <Button
                  disabled={!selectedImage.preview || loading}
                  onClick={goToNextStep}
                >
                  {loading ? (
                    <span className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Analyzing...
                    </span>
                  ) : (
                    <span className="flex items-center">
                      Continue
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </span>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        );
      case 1:
        return (
          <div className={`transition-opacity duration-300 ${fadeClass}`}>
            <Card>
              <CardHeader>
                <CardTitle>Analysis Results</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-3">Identified Items</h3>
                    <div className="space-y-4">
                      {analysisResults.map((item, index) => (
                        <div
                          key={index}
                          className="p-4 bg-white border rounded-lg shadow-sm"
                        >
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">{item.foodItem}</h4>
                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">
                              {Math.round(item.confidence * 100)}% Confidence
                            </span>
                          </div>
                          <div className="mt-2">
                            <Label className="text-sm text-gray-500">Portion</Label>
                            <Select
                              defaultValue={item.selectedPortion}
                              onValueChange={(value) => {
                                const updatedResults = [...analysisResults];
                                updatedResults[index].selectedPortion = value;
                                setAnalysisResults(updatedResults);
                              }}
                            >
                              <SelectTrigger className="w-full mt-1">
                                <SelectValue placeholder="Select portion" />
                              </SelectTrigger>
                              <SelectContent>
                                {item.commonPortions.map((portion: string) => (
                                  <SelectItem key={portion} value={portion}>
                                    {portion}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="mt-3 flex items-center justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={() => {
                                const updatedResults = analysisResults.filter(
                                  (_, i) => i !== index
                                );
                                setAnalysisResults(updatedResults);
                                toast.success(`Removed ${item.foodItem}`);
                              }}
                            >
                              Remove
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-nutrisnap-teal border-nutrisnap-teal hover:bg-nutrisnap-teal hover:text-white"
                              onClick={() => {
                                const updatedResults = [...analysisResults];
                                updatedResults[index].userVerified = true;
                                setAnalysisResults(updatedResults);
                                toast.success(`Verified ${item.foodItem}`);
                              }}
                            >
                              {item.userVerified ? (
                                <>
                                  <Check className="mr-1 h-4 w-4" />
                                  Verified
                                </>
                              ) : (
                                "Verify"
                              )}
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                    <Button
                      variant="outline"
                      className="w-full mt-4"
                      onClick={() => {
                        // Add mock food item
                        const newItem = {
                          foodItem: "Added Food Item",
                          confidence: 1.0,
                          boundingBox: { x: 0, y: 0, width: 0, height: 0 },
                          quantityGrams: 100,
                          commonPortions: ["1 serving", "2 servings"],
                          selectedPortion: "1 serving",
                          userVerified: true,
                          userAdded: true,
                        };
                        setAnalysisResults([...analysisResults, newItem]);
                      }}
                    >
                      Add Food Item
                    </Button>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium mb-3">Image Preview</h3>
                    <div className="relative border rounded-lg overflow-hidden">
                      {selectedImage.preview && (
                        <img
                          src={selectedImage.preview}
                          alt="Food preview"
                          className="w-full h-auto"
                        />
                      )}
                      {/* Overlay highlighting detected items would go here in a real app */}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={goToPreviousStep}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button onClick={goToNextStep}>
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        );
      case 2:
        return (
          <div className={`transition-opacity duration-300 ${fadeClass}`}>
            <Card>
              <CardHeader>
                <CardTitle>Nutritional Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <h3 className="text-lg font-medium mb-3">Nutrition Summary</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <NutritionCard
                        data={{
                          label: "Calories",
                          value: nutritionalSummary.calories,
                          unit: "kcal",
                          goal: user?.preferences.nutritionGoals.calories || 2000,
                          color: "blue",
                        }}
                      />
                      <NutritionCard
                        data={{
                          label: "Protein",
                          value: nutritionalSummary.protein,
                          unit: "g",
                          goal: user?.preferences.nutritionGoals.protein || 120,
                          color: "red",
                        }}
                      />
                      <NutritionCard
                        data={{
                          label: "Carbs",
                          value: nutritionalSummary.carbs,
                          unit: "g",
                          goal: user?.preferences.nutritionGoals.carbs || 250,
                          color: "green",
                        }}
                      />
                      <NutritionCard
                        data={{
                          label: "Fat",
                          value: nutritionalSummary.fat,
                          unit: "g",
                          goal: user?.preferences.nutritionGoals.fat || 70,
                          color: "yellow",
                        }}
                      />
                    </div>

                    <h3 className="text-lg font-medium mb-3">Micronutrients</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Fiber</span>
                          <span className="text-sm text-gray-500">
                            {nutritionalSummary.fiber}g
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{
                              width: `${Math.min(
                                (nutritionalSummary.fiber / 25) * 100,
                                100
                              )}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Sugar</span>
                          <span className="text-sm text-gray-500">
                            {nutritionalSummary.sugar}g
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-orange-500 h-2 rounded-full"
                            style={{
                              width: `${Math.min(
                                (nutritionalSummary.sugar / 25) * 100,
                                100
                              )}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Sodium</span>
                          <span className="text-sm text-gray-500">
                            {nutritionalSummary.sodium}mg
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full"
                            style={{
                              width: `${Math.min(
                                (nutritionalSummary.sodium / 2300) * 100,
                                100
                              )}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="lg:col-span-1">
                    <h3 className="text-lg font-medium mb-3">Food Items</h3>
                    <div className="space-y-2">
                      {analysisResults.map((item, index) => (
                        <div
                          key={index}
                          className="p-3 bg-gray-50 rounded-md border"
                        >
                          <div className="flex justify-between">
                            <span className="font-medium">{item.foodItem}</span>
                            <span className="text-sm text-gray-500">
                              {item.quantityGrams}g
                            </span>
                          </div>
                          <div className="text-sm text-gray-500">
                            {item.selectedPortion}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={goToPreviousStep}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button onClick={goToNextStep}>
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        );
      case 3:
        return (
          <div className={`transition-opacity duration-300 ${fadeClass}`}>
            <Card>
              <CardHeader>
                <CardTitle>Save to Meal History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="mealCategory">Meal Category</Label>
                      <Select
                        value={mealDetails.category}
                        onValueChange={(value) =>
                          setMealDetails({ ...mealDetails, category: value })
                        }
                      >
                        <SelectTrigger id="mealCategory">
                          <SelectValue placeholder="Select meal category" />
                        </SelectTrigger>
                        <SelectContent>
                          {mealCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="mealDateTime">Date and Time</Label>
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                        <Input
                          id="mealDateTime"
                          type="datetime-local"
                          value={mealDetails.dateTime}
                          onChange={(e) =>
                            setMealDetails({
                              ...mealDetails,
                              dateTime: e.target.value,
                            })
                          }
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="mealNotes">Notes</Label>
                      <Textarea
                        id="mealNotes"
                        placeholder="Add any notes about this meal..."
                        value={mealDetails.notes}
                        onChange={(e) =>
                          setMealDetails({
                            ...mealDetails,
                            notes: e.target.value,
                          })
                        }
                        rows={4}
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Meal Summary</h3>
                    <div className="bg-gray-50 rounded-lg p-4 border">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <p className="text-sm text-gray-500">Calories</p>
                          <p className="text-xl font-bold">
                            {nutritionalSummary.calories}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Protein</p>
                          <p className="text-xl font-bold">
                            {nutritionalSummary.protein}g
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Carbs</p>
                          <p className="text-xl font-bold">
                            {nutritionalSummary.carbs}g
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Fat</p>
                          <p className="text-xl font-bold">
                            {nutritionalSummary.fat}g
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium">Items</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-600">
                        {analysisResults.map((item, index) => (
                          <li key={index}>
                            {item.foodItem} ({item.selectedPortion})
                          </li>
                        ))}
                      </ul>
                    </div>
                    {selectedImage.preview && (
                      <div className="mt-4">
                        <img
                          src={selectedImage.preview}
                          alt="Meal"
                          className="w-full h-auto rounded-md"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={goToPreviousStep}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button onClick={handleSaveMeal} disabled={loading}>
                  {loading ? (
                    <span className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Saving...
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <Check className="mr-2 h-4 w-4" />
                      Save Meal
                    </span>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 p-4 md:p-8">
        <div className="max-w-5xl mx-auto">
          <div className="mb-8">
            <h1 className="text-2xl md:text-3xl font-bold text-nutrisnap-charcoal">
              Snap New Meal
            </h1>
            <p className="text-gray-500 mt-1">
              Follow the steps to analyze your meal
            </p>
          </div>

          {/* Step Progress */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center mb-2 ${
                      index === currentStep
                        ? "bg-nutrisnap-teal text-white"
                        : index < currentStep
                        ? "bg-green-500 text-white"
                        : "bg-gray-200 text-gray-500"
                    }`}
                  >
                    {index < currentStep ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <span
                    className={`text-xs hidden md:block ${
                      index === currentStep
                        ? "text-nutrisnap-teal font-medium"
                        : "text-gray-500"
                    }`}
                  >
                    {step}
                  </span>
                </div>
              ))}
            </div>
            <div className="relative mt-2">
              <div className="absolute top-0 left-0 h-1 bg-gray-200 w-full rounded"></div>
              <div
                className="absolute top-0 left-0 h-1 bg-nutrisnap-teal rounded transition-all duration-500"
                style={{
                  width: `${(currentStep / (steps.length - 1)) * 100}%`,
                }}
              ></div>
            </div>
          </div>

          {/* Step Content */}
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
};

export default SnapNew;
