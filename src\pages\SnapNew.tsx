
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import UploadZone from "@/components/upload/UploadZone";
import { mealCategories } from "@/data/mockData";
import { ArrowLeft, ArrowRight, Calendar, Check, AlertCircle, RefreshCw } from "lucide-react";
import NutritionCard from "@/components/nutrition/NutritionCard";
import { useUploadFoodAnalysis } from "@/hooks/use-food-analysis";
import { useAIAnalysis, ProcessedFoodItem, ProcessedNutritionalSummary } from "@/hooks/use-ai-analysis";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import AIAnalysisResults from "@/components/ai/AIAnalysisResults";
import AIAnalysisProgress from "@/components/ai/AIAnalysisProgress";

const steps = [
  "Upload Image",
  "Analysis Results",
  "Nutritional Details",
  "Save to History",
];

const SnapNew = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { analyzeImage, processAnalysisResult, isAnalyzing, analysisResult, error, resetAnalysis, retryAnalysis } = useAIAnalysis();
  const uploadMutation = useUploadFoodAnalysis();

  const [currentStep, setCurrentStep] = useState(0);
  const [selectedImage, setSelectedImage] = useState<{
    file: File | null;
    preview: string | null;
  }>({
    file: null,
    preview: null,
  });
  const [analysisResults, setAnalysisResults] = useState<ProcessedFoodItem[]>([]);
  const [nutritionalSummary, setNutritionalSummary] = useState<ProcessedNutritionalSummary>({
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    sodium: 0,
  });
  const [mealDetails, setMealDetails] = useState({
    category: "breakfast",
    dateTime: new Date().toISOString().slice(0, 16),
    notes: "",
  });
  const [loading, setLoading] = useState(false);
  const [fadeClass, setFadeClass] = useState("");

  const handleImageSelected = async (file: File, previewUrl: string) => {
    setSelectedImage({
      file,
      preview: previewUrl,
    });

    // Start AI analysis
    await performAIAnalysis(file);
  };

  const performAIAnalysis = async (file: File) => {
    try {
      console.log('🔍 Starting AI analysis...');
      const result = await analyzeImage(file);

      if (result && result.success) {
        const processed = processAnalysisResult(result);

        if (processed) {
          setAnalysisResults(processed.foodItems);
          setNutritionalSummary(processed.nutritionalSummary);

          // Wait a moment then move to the next step
          setTimeout(() => {
            goToNextStep();
          }, 1000);
        }
      }
    } catch (error) {
      console.error('❌ Analysis failed:', error);
      // Error is already handled by the hook
    }
  };

  const handleRetryAnalysis = async () => {
    if (selectedImage.file) {
      await performAIAnalysis(selectedImage.file);
    }
  };

  const goToNextStep = () => {
    setFadeClass("opacity-0");
    setTimeout(() => {
      setCurrentStep((prev) => prev + 1);
      setFadeClass("opacity-100");
    }, 300);
  };

  const goToPreviousStep = () => {
    setFadeClass("opacity-0");
    setTimeout(() => {
      setCurrentStep((prev) => prev - 1);
      setFadeClass("opacity-100");
    }, 300);
  };

  const handleSaveMeal = async () => {
    setLoading(true);

    try {
      // Prepare data for backend
      const uploadData = {
        image: selectedImage.file!,
        mealCategory: mealDetails.category,
        mealDateTime: mealDetails.dateTime,
        recognitionResults: analysisResults.map(item => ({
          foodItem: item.name,
          confidence: item.confidence,
          boundingBox: { x: 0, y: 0, width: 100, height: 100 }, // Default bounding box
          quantityGrams: 100, // Default quantity
          commonPortions: [item.portionSize],
          selectedPortion: item.portionSize,
          userVerified: item.userVerified,
          userAdded: item.userAdded || false,
          // Additional nutrition data for backend processing
          calories: item.calories,
          protein: item.protein,
          carbohydrates: item.carbohydrates,
          fats: item.fats,
          notes: item.notes
        })),
        nutritionalSummary: {
          calories: nutritionalSummary.calories,
          protein: nutritionalSummary.protein,
          carbs: nutritionalSummary.carbs,
          fat: nutritionalSummary.fat,
          fiber: nutritionalSummary.fiber,
          sugar: nutritionalSummary.sugar,
          sodium: nutritionalSummary.sodium
        },
        userNotes: mealDetails.notes
      };

      // Call the upload mutation
      await uploadMutation.mutateAsync(uploadData);

      // Success - redirect to meal history with success parameter
      toast.success("Meal saved successfully! 🎉");
      setTimeout(() => {
        navigate("/meal-history?saved=true");
      }, 1000);

    } catch (error) {
      console.error('❌ Save meal error:', error);
      toast.error("Failed to save meal. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className={`transition-opacity duration-300 ${fadeClass}`}>
            <Card className="border-dashed">
              <CardHeader>
                <CardTitle>Upload Your Food Image</CardTitle>
                <p className="text-sm text-gray-600 mt-2">
                  Upload a clear image of your meal for AI-powered nutritional analysis
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <UploadZone onImageSelected={handleImageSelected} />

                {/* Enhanced AI Analysis Progress */}
                {isAnalyzing && (
                  <div className="mt-6">
                    <AIAnalysisProgress
                      isAnalyzing={isAnalyzing}
                      onComplete={() => {
                        // Progress component handles its own completion
                      }}
                    />
                  </div>
                )}

                {/* Error Display */}
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Analysis Failed</AlertTitle>
                    <AlertDescription className="flex items-center justify-between">
                      <span>{error}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRetryAnalysis}
                        disabled={!selectedImage.file || isAnalyzing}
                      >
                        <RefreshCw className="h-4 w-4 mr-1" />
                        Retry
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Success Message */}
                {analysisResult && analysisResult.success && (
                  <Alert className="border-green-200 bg-green-50">
                    <Check className="h-4 w-4 text-green-600" />
                    <AlertTitle className="text-green-800">Analysis Complete!</AlertTitle>
                    <AlertDescription className="text-green-700">
                      Found {analysisResult.data?.analysis.food_items.length || 0} food items.
                      Click continue to review the results.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => navigate("/dashboard")}
                  disabled={isAnalyzing}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Dashboard
                </Button>
                <Button
                  disabled={!selectedImage.preview || isAnalyzing || !analysisResult?.success}
                  onClick={goToNextStep}
                >
                  {isAnalyzing ? (
                    <span className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Analyzing...
                    </span>
                  ) : (
                    <span className="flex items-center">
                      Continue
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </span>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        );
      case 1:
        return (
          <div className={`transition-opacity duration-300 ${fadeClass}`}>
            <Card>
              <CardHeader>
                <CardTitle>AI Analysis Results</CardTitle>
                <p className="text-gray-500">
                  Review and verify the AI-detected food items and nutritional information
                </p>
              </CardHeader>
              <CardContent>
                {/* Use the new AIAnalysisResults component */}
                <AIAnalysisResults
                  foodItems={analysisResults}
                  nutritionalSummary={nutritionalSummary}
                  overallNotes={analysisResult?.data?.analysis.overall_notes}
                  isLoading={isAnalyzing}
                  error={error}
                  onRetry={handleRetryAnalysis}
                  onEditItem={(index, item) => {
                    // Handle editing food items
                    const updatedResults = [...analysisResults];
                    updatedResults[index] = item;
                    setAnalysisResults(updatedResults);

                    // Recalculate nutritional summary
                    const newSummary = updatedResults.reduce(
                      (acc, foodItem) => ({
                        calories: acc.calories + foodItem.calories,
                        protein: acc.protein + foodItem.protein,
                        carbs: acc.carbs + foodItem.carbohydrates,
                        fat: acc.fat + foodItem.fats,
                        fiber: acc.fiber,
                        sugar: acc.sugar,
                        sodium: acc.sodium,
                      }),
                      { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0, sugar: 0, sodium: 0 }
                    );
                    setNutritionalSummary(newSummary);
                  }}
                  onAddItem={(item) => {
                    // Handle adding new food items
                    const updatedResults = [...analysisResults, item];
                    setAnalysisResults(updatedResults);

                    // Recalculate nutritional summary
                    const newSummary = updatedResults.reduce(
                      (acc, foodItem) => ({
                        calories: acc.calories + foodItem.calories,
                        protein: acc.protein + foodItem.protein,
                        carbs: acc.carbs + foodItem.carbohydrates,
                        fat: acc.fat + foodItem.fats,
                        fiber: acc.fiber,
                        sugar: acc.sugar,
                        sodium: acc.sodium,
                      }),
                      { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0, sugar: 0, sodium: 0 }
                    );
                    setNutritionalSummary(newSummary);
                  }}
                  onRemoveItem={(index) => {
                    // Handle removing food items
                    const updatedResults = analysisResults.filter((_, i) => i !== index);
                    setAnalysisResults(updatedResults);

                    // Recalculate nutritional summary
                    const newSummary = updatedResults.reduce(
                      (acc, foodItem) => ({
                        calories: acc.calories + foodItem.calories,
                        protein: acc.protein + foodItem.protein,
                        carbs: acc.carbs + foodItem.carbohydrates,
                        fat: acc.fat + foodItem.fats,
                        fiber: acc.fiber,
                        sugar: acc.sugar,
                        sodium: acc.sodium,
                      }),
                      { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0, sugar: 0, sodium: 0 }
                    );
                    setNutritionalSummary(newSummary);
                  }}
                />

                {/* Legacy code for reference - can be removed after testing */}
                <div className="hidden">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-lg font-medium mb-3">Identified Items</h3>
                      <div className="space-y-4">
                        {analysisResults.map((item, index) => (
                        <div
                          key={index}
                          className="p-4 bg-white border rounded-lg shadow-sm"
                        >
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">{item.name}</h4>
                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">
                              {Math.round(item.confidence)}% Confidence
                            </span>
                          </div>
                          <div className="mt-2">
                            <Label className="text-sm text-gray-500">Portion Size</Label>
                            <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                              {item.portionSize}
                            </div>
                          </div>
                          <div className="mt-2 grid grid-cols-2 gap-2 text-xs text-gray-600">
                            <div>Calories: {item.calories}</div>
                            <div>Protein: {item.protein}g</div>
                            <div>Carbs: {item.carbohydrates}g</div>
                            <div>Fat: {item.fats}g</div>
                          </div>
                          {item.notes && (
                            <div className="mt-2 text-xs text-gray-500 italic">
                              {item.notes}
                            </div>
                          )}
                          <div className="mt-3 flex items-center justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={() => {
                                const updatedResults = analysisResults.filter(
                                  (_, i) => i !== index
                                );
                                setAnalysisResults(updatedResults);
                                toast.success(`Removed ${item.name}`);
                              }}
                            >
                              Remove
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-nutrisnap-teal border-nutrisnap-teal hover:bg-nutrisnap-teal hover:text-white"
                              onClick={() => {
                                const updatedResults = [...analysisResults];
                                updatedResults[index].userVerified = true;
                                setAnalysisResults(updatedResults);
                                toast.success(`Verified ${item.name}`);
                              }}
                            >
                              {item.userVerified ? (
                                <>
                                  <Check className="mr-1 h-4 w-4" />
                                  Verified
                                </>
                              ) : (
                                "Verify"
                              )}
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                    <Button
                      variant="outline"
                      className="w-full mt-4"
                      onClick={() => {
                        // Add manual food item
                        const newItem: ProcessedFoodItem = {
                          name: "Manual Food Item",
                          portionSize: "1 serving",
                          calories: 100,
                          protein: 5,
                          carbohydrates: 15,
                          fats: 3,
                          confidence: 100,
                          notes: "Manually added by user",
                          userVerified: true,
                          userAdded: true,
                        };
                        setAnalysisResults([...analysisResults, newItem]);
                        toast.success("Food item added successfully");
                      }}
                    >
                      Add Food Item
                    </Button>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium mb-3">Image Preview</h3>
                    <div className="relative border rounded-lg overflow-hidden">
                      {selectedImage.preview && (
                        <img
                          src={selectedImage.preview}
                          alt="Food preview"
                          className="w-full h-auto"
                        />
                      )}
                      {/* Overlay highlighting detected items would go here in a real app */}
                    </div>
                  </div>
                </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={goToPreviousStep}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button onClick={goToNextStep}>
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        );
      case 2:
        return (
          <div className={`transition-opacity duration-300 ${fadeClass}`}>
            <Card>
              <CardHeader>
                <CardTitle>Nutritional Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <h3 className="text-lg font-medium mb-3">Nutrition Summary</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <NutritionCard
                        data={{
                          label: "Calories",
                          value: nutritionalSummary.calories,
                          unit: "kcal",
                          goal: user?.preferences.nutritionGoals.calories || 2000,
                          color: "blue",
                        }}
                      />
                      <NutritionCard
                        data={{
                          label: "Protein",
                          value: nutritionalSummary.protein,
                          unit: "g",
                          goal: user?.preferences.nutritionGoals.protein || 120,
                          color: "red",
                        }}
                      />
                      <NutritionCard
                        data={{
                          label: "Carbs",
                          value: nutritionalSummary.carbs,
                          unit: "g",
                          goal: user?.preferences.nutritionGoals.carbs || 250,
                          color: "green",
                        }}
                      />
                      <NutritionCard
                        data={{
                          label: "Fat",
                          value: nutritionalSummary.fat,
                          unit: "g",
                          goal: user?.preferences.nutritionGoals.fat || 70,
                          color: "yellow",
                        }}
                      />
                    </div>

                    <h3 className="text-lg font-medium mb-3">Micronutrients</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Fiber</span>
                          <span className="text-sm text-gray-500">
                            {nutritionalSummary.fiber}g
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{
                              width: `${Math.min(
                                (nutritionalSummary.fiber / 25) * 100,
                                100
                              )}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Sugar</span>
                          <span className="text-sm text-gray-500">
                            {nutritionalSummary.sugar}g
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-orange-500 h-2 rounded-full"
                            style={{
                              width: `${Math.min(
                                (nutritionalSummary.sugar / 25) * 100,
                                100
                              )}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Sodium</span>
                          <span className="text-sm text-gray-500">
                            {nutritionalSummary.sodium}mg
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full"
                            style={{
                              width: `${Math.min(
                                (nutritionalSummary.sodium / 2300) * 100,
                                100
                              )}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="lg:col-span-1">
                    <h3 className="text-lg font-medium mb-3">Food Items</h3>
                    <div className="space-y-2">
                      {analysisResults.map((item, index) => (
                        <div
                          key={index}
                          className="p-3 bg-gray-50 rounded-md border"
                        >
                          <div className="flex justify-between">
                            <span className="font-medium">{item.name}</span>
                            <span className="text-sm text-gray-500">
                              {item.calories} cal
                            </span>
                          </div>
                          <div className="text-sm text-gray-500">
                            {item.portionSize}
                          </div>
                          {item.userAdded && (
                            <div className="text-xs text-blue-600 mt-1">
                              Manually added
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={goToPreviousStep}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button onClick={goToNextStep}>
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        );
      case 3:
        return (
          <div className={`transition-opacity duration-300 ${fadeClass}`}>
            <Card>
              <CardHeader>
                <CardTitle>Save to Meal History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="mealCategory">Meal Category</Label>
                      <Select
                        value={mealDetails.category}
                        onValueChange={(value) =>
                          setMealDetails({ ...mealDetails, category: value })
                        }
                      >
                        <SelectTrigger id="mealCategory">
                          <SelectValue placeholder="Select meal category" />
                        </SelectTrigger>
                        <SelectContent>
                          {mealCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="mealDateTime">Date and Time</Label>
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                        <Input
                          id="mealDateTime"
                          type="datetime-local"
                          value={mealDetails.dateTime}
                          onChange={(e) =>
                            setMealDetails({
                              ...mealDetails,
                              dateTime: e.target.value,
                            })
                          }
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="mealNotes">Notes</Label>
                      <Textarea
                        id="mealNotes"
                        placeholder="Add any notes about this meal..."
                        value={mealDetails.notes}
                        onChange={(e) =>
                          setMealDetails({
                            ...mealDetails,
                            notes: e.target.value,
                          })
                        }
                        rows={4}
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Meal Summary</h3>
                    <div className="bg-gray-50 rounded-lg p-4 border">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <p className="text-sm text-gray-500">Calories</p>
                          <p className="text-xl font-bold">
                            {nutritionalSummary.calories}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Protein</p>
                          <p className="text-xl font-bold">
                            {nutritionalSummary.protein}g
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Carbs</p>
                          <p className="text-xl font-bold">
                            {nutritionalSummary.carbs}g
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Fat</p>
                          <p className="text-xl font-bold">
                            {nutritionalSummary.fat}g
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium">Items</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-600">
                        {analysisResults.map((item, index) => (
                          <li key={index}>
                            {item.name} ({item.portionSize})
                            {item.userAdded && (
                              <span className="text-blue-600 text-xs ml-2">[Manual]</span>
                            )}
                          </li>
                        ))}
                      </ul>
                    </div>
                    {selectedImage.preview && (
                      <div className="mt-4">
                        <img
                          src={selectedImage.preview}
                          alt="Meal"
                          className="w-full h-auto rounded-md"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={goToPreviousStep}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <Button onClick={handleSaveMeal} disabled={loading}>
                  {loading ? (
                    <span className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Saving...
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <Check className="mr-2 h-4 w-4" />
                      Save Meal
                    </span>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 p-4 md:p-8">
        <div className="max-w-5xl mx-auto">
          <div className="mb-8">
            <h1 className="text-2xl md:text-3xl font-bold text-nutrisnap-charcoal">
              Snap New Meal
            </h1>
            <p className="text-gray-500 mt-1">
              Follow the steps to analyze your meal
            </p>
          </div>

          {/* Step Progress */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center mb-2 ${
                      index === currentStep
                        ? "bg-nutrisnap-teal text-white"
                        : index < currentStep
                        ? "bg-green-500 text-white"
                        : "bg-gray-200 text-gray-500"
                    }`}
                  >
                    {index < currentStep ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <span
                    className={`text-xs hidden md:block ${
                      index === currentStep
                        ? "text-nutrisnap-teal font-medium"
                        : "text-gray-500"
                    }`}
                  >
                    {step}
                  </span>
                </div>
              ))}
            </div>
            <div className="relative mt-2">
              <div className="absolute top-0 left-0 h-1 bg-gray-200 w-full rounded"></div>
              <div
                className="absolute top-0 left-0 h-1 bg-nutrisnap-teal rounded transition-all duration-500"
                style={{
                  width: `${(currentStep / (steps.length - 1)) * 100}%`,
                }}
              ></div>
            </div>
          </div>

          {/* Step Content */}
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
};

export default SnapNew;
