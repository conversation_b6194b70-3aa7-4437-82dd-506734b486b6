import apiClient from '@/lib/api-client';
import { GalleryItem } from './admin.service';

// Public Gallery Types
export interface PublicGalleryItem {
  id?: string;
  _id?: string;
  title: string;
  description?: string;
  imageUrl: string;
  category: string;
  slug: string;
  featured?: boolean;
  isFeatured?: boolean;
  tags?: string[];
  altText?: string;
  displayOrder?: number;
  createdAt: string;
}

export interface GalleryCategory {
  name: string;
  slug: string;
  count: number;
  description?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    galleryItems: T[];
    pagination: {
      current: number;
      pages: number;
      total: number;
      limit: number;
    };
  };
}

// Public Gallery Service
const GalleryService = {
  // Get public gallery items
  getGalleryItems: async (params?: {
    page?: number;
    limit?: number;
    category?: string;
    featured?: boolean;
    search?: string;
  }): Promise<{ success: boolean; data: PublicGalleryItem[]; pagination?: any }> => {
    const response = await apiClient.get('/gallery', { params });
    return {
      success: response.data.success,
      data: response.data.data?.galleryItems || [],
      pagination: response.data.data?.pagination
    };
  },

  // Get featured gallery items
  getFeaturedItems: async (limit?: number): Promise<PublicGalleryItem[]> => {
    const response = await apiClient.get('/gallery/featured', {
      params: { limit },
    });
    return response.data.data?.featuredItems || [];
  },

  // Get gallery categories
  getCategories: async (): Promise<GalleryCategory[]> => {
    const response = await apiClient.get<ApiResponse<GalleryCategory[]>>('/gallery/categories');
    return response.data.data;
  },

  // Get gallery item by slug
  getItemBySlug: async (slug: string): Promise<PublicGalleryItem> => {
    const response = await apiClient.get<ApiResponse<PublicGalleryItem>>(`/gallery/${slug}`);
    return response.data.data;
  },
};

export default GalleryService;
