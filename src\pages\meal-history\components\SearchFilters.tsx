
import React from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Filter, Calendar } from "lucide-react";
import { mealCategories } from "@/data/mockData";

interface SearchFiltersProps {
  searchTerm: string;
  selectedCategory: string | null;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  handleSearch: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleCategoryChange: (value: string) => void;
  handleDateChange: (field: "startDate" | "endDate", value: string) => void;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  searchTerm,
  selectedCategory,
  dateRange,
  handleSearch,
  handleCategoryChange,
  handleDateChange,
}) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm mb-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search meals..."
            className="pl-10"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="text-gray-400" />
          <Select
            value={selectedCategory || "all"}
            onValueChange={handleCategoryChange}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {mealCategories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid grid-cols-2 gap-2">
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="date"
              className="pl-10"
              placeholder="Start date"
              value={dateRange.startDate}
              onChange={(e) =>
                handleDateChange("startDate", e.target.value)
              }
            />
          </div>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="date"
              className="pl-10"
              placeholder="End date"
              value={dateRange.endDate}
              onChange={(e) =>
                handleDateChange("endDate", e.target.value)
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchFilters;
