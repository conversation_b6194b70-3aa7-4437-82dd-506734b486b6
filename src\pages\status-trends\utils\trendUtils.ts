
// Create our own interface since FoodAnalysis isn't exported from mockData
export interface FoodAnalysis {
  id: string;
  userId: string;
  imageUrl: string;
  mealCategory: string;
  mealDateTime: string;
  userNotes: string;
  recognitionResults: Array<{
    foodItem: string;
    confidence: number;
    boundingBox: { x: number; y: number; width: number; height: number };
    quantityGrams: number;
    commonPortions: string[];
    selectedPortion: string;
    userVerified: boolean;
    userAdded?: boolean;
  }>;
  nutritionalSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
  };
}

export interface TrendDataPoint {
  date: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  analysisCount: number;
}

export interface SummaryStats {
  avgCalories: number;
  avgProtein: number;
  avgCarbs: number;
  avgFat: number;
  trackingRate: number;
}

// Generate data for calendar heatmap
export const generateCalendarData = (analyses: FoodAnalysis[]): Record<string, number> => {
  const today = new Date();
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(today.getMonth() - 6);
  
  const dates: Record<string, number> = {};
  
  // Initialize all dates in the past 6 months with 0
  for (let d = new Date(sixMonthsAgo); d <= today; d.setDate(d.getDate() + 1)) {
    const dateStr = d.toISOString().split('T')[0];
    dates[dateStr] = 0;
  }
  
  // Count analyses per day
  analyses.forEach(analysis => {
    const dateStr = new Date(analysis.mealDateTime).toISOString().split('T')[0];
    if (dates[dateStr] !== undefined) {
      dates[dateStr] += 1;
    }
  });
  
  return dates;
};

// Generate trend data for the last 30 days
export const generateTrendData = (analyses: FoodAnalysis[]): TrendDataPoint[] => {
  // Get the last 30 days
  const result = [];
  const end = new Date();
  const start = new Date();
  start.setDate(end.getDate() - 30);
  
  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    const dateStr = d.toISOString().split('T')[0];
    
    // Find analyses for this date
    const dayAnalyses = analyses.filter(analysis => {
      const analysisDate = new Date(analysis.mealDateTime).toISOString().split('T')[0];
      return analysisDate === dateStr;
    });
    
    // Sum up nutritional data
    const daySummary = dayAnalyses.reduce(
      (acc, analysis) => {
        acc.calories += analysis.nutritionalSummary.calories;
        acc.protein += analysis.nutritionalSummary.protein;
        acc.carbs += analysis.nutritionalSummary.carbs;
        acc.fat += analysis.nutritionalSummary.fat;
        return acc;
      },
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
    
    result.push({
      date: dateStr,
      ...daySummary,
      analysisCount: dayAnalyses.length,
    });
  }
  
  return result;
};

// Calculate summary statistics
export const calculateSummary = (trendData: TrendDataPoint[]): SummaryStats => {
  const nonZeroDays = trendData.filter(day => day.analysisCount > 0);
  
  if (nonZeroDays.length === 0) {
    return {
      avgCalories: 0,
      avgProtein: 0,
      avgCarbs: 0,
      avgFat: 0,
      trackingRate: 0,
    };
  }
  
  const avgCalories = nonZeroDays.reduce((sum, day) => sum + day.calories, 0) / nonZeroDays.length;
  const avgProtein = nonZeroDays.reduce((sum, day) => sum + day.protein, 0) / nonZeroDays.length;
  const avgCarbs = nonZeroDays.reduce((sum, day) => sum + day.carbs, 0) / nonZeroDays.length;
  const avgFat = nonZeroDays.reduce((sum, day) => sum + day.fat, 0) / nonZeroDays.length;
  const trackingRate = (nonZeroDays.length / trendData.length) * 100;
  
  return {
    avgCalories: Math.round(avgCalories),
    avgProtein: Math.round(avgProtein),
    avgCarbs: Math.round(avgCarbs),
    avgFat: Math.round(avgFat),
    trackingRate: Math.round(trackingRate),
  };
};
