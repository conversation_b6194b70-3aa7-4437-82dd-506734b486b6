
import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Calendar, Clock, MessageSquare, Tag, ArrowLeft, AlertCircle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useFoodAnalysis } from "@/hooks/use-food-analysis";

interface FoodItem {
  foodItem: string;
  confidence: number;
  boundingBox: { x: number; y: number; width: number; height: number };
  quantityGrams: number;
  commonPortions: string[];
  selectedPortion: string;
  userVerified: boolean;
  userAdded?: boolean;
}

interface Meal {
  id: string;
  imageUrl: string;
  userNotes: string;
  mealCategory: string;
  mealDateTime: string;
  recognitionResults: FoodItem[];
  nutritionalSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
  };
}

const MealDetailsPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Fetch meal details from API
  const { data, isLoading, error } = useFoodAnalysis(id || '');
  const meal = data?.foodAnalysis;

  const handleBack = () => {
    navigate(-1);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen bg-gray-50">
        <div className="flex-1 p-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6 flex items-center">
              <Button
                variant="ghost"
                className="mr-4"
                onClick={handleBack}
              >
                <ArrowLeft className="mr-2 h-5 w-5" />
                Back
              </Button>
              <Skeleton className="h-10 w-40" />
            </div>

            <div className="bg-white p-6 rounded-xl shadow-md space-y-8">
              <Skeleton className="h-80 w-full rounded-xl" />
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <div className="space-y-3">
                  <Skeleton className="h-6 w-1/2" />
                  <Skeleton className="h-6 w-1/3" />
                </div>
              </div>
              <Separator />
              <div>
                <Skeleton className="h-8 w-1/4 mb-4" />
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[...Array(7)].map((_, i) => (
                    <Skeleton key={i} className="h-20 rounded-lg" />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !meal) {
    return (
      <div className="flex min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto w-full">
          <div className="mb-6 flex items-center">
            <Button
              variant="ghost"
              className="mr-4"
              onClick={handleBack}
            >
              <ArrowLeft className="mr-2 h-5 w-5" />
              Back
            </Button>
            <h1 className="text-3xl font-bold text-nutrisnap-charcoal">
              Meal Details
            </h1>
          </div>

          <Alert variant="destructive" className="my-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error ? "Failed to load meal details. Please try again later." : "Meal not found."}
            </AlertDescription>
          </Alert>

          <Button onClick={handleBack} className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Return to Meal History
          </Button>
        </div>
      </div>
    );
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });
  };

  const getMealCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case "breakfast":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "lunch":
        return "bg-green-100 text-green-800 border-green-200";
      case "dinner":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "snack":
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatNutritionValue = (value: number) => {
    return Math.round(value);
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6 flex items-center">
            <Button
              variant="ghost"
              className="mr-4"
              onClick={handleBack}
            >
              <ArrowLeft className="mr-2 h-5 w-5" />
              Back
            </Button>
            <h1 className="text-3xl font-bold text-nutrisnap-charcoal">
              Meal Details
            </h1>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-md space-y-8">
            {/* Image and basic info */}
            <div className="relative rounded-xl overflow-hidden h-80">
              <img
                src={meal.imageUrl}
                alt={meal.userNotes}
                className="w-full h-full object-cover"
              />
              <Badge
                className={`absolute top-4 right-4 text-sm py-1 px-3 ${getMealCategoryColor(
                  meal.mealCategory
                )}`}
              >
                {meal.mealCategory.charAt(0).toUpperCase() +
                  meal.mealCategory.slice(1)}
              </Badge>
            </div>

            {/* Meal Info */}
            <div className="space-y-4">
              <h3 className="text-2xl font-semibold">{meal.userNotes}</h3>

              <div className="flex flex-col space-y-3">
                <div className="flex items-center text-gray-600">
                  <Calendar className="h-5 w-5 mr-2" />
                  {formatDate(meal.mealDateTime)}
                </div>
                <div className="flex items-center text-gray-600">
                  <Clock className="h-5 w-5 mr-2" />
                  {formatTime(meal.mealDateTime)} ({getTimeAgo(meal.mealDateTime)})
                </div>
                {meal.userNotes && (
                  <div className="flex items-start text-gray-600">
                    <MessageSquare className="h-5 w-5 mr-2 mt-0.5" />
                    {meal.userNotes}
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Nutritional Summary */}
            <div>
              <h4 className="text-xl font-semibold mb-4">Nutritional Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600">Calories</div>
                  <div className="text-2xl font-semibold">
                    {formatNutritionValue(meal.nutritionalSummary.calories)}
                  </div>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600">Protein</div>
                  <div className="text-2xl font-semibold">
                    {formatNutritionValue(meal.nutritionalSummary.protein)}g
                  </div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600">Carbs</div>
                  <div className="text-2xl font-semibold">
                    {formatNutritionValue(meal.nutritionalSummary.carbs)}g
                  </div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600">Fat</div>
                  <div className="text-2xl font-semibold">
                    {formatNutritionValue(meal.nutritionalSummary.fat)}g
                  </div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600">Fiber</div>
                  <div className="text-2xl font-semibold">
                    {formatNutritionValue(meal.nutritionalSummary.fiber)}g
                  </div>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600">Sugar</div>
                  <div className="text-2xl font-semibold">
                    {formatNutritionValue(meal.nutritionalSummary.sugar)}g
                  </div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600">Sodium</div>
                  <div className="text-2xl font-semibold">
                    {formatNutritionValue(meal.nutritionalSummary.sodium)}mg
                  </div>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>
  );
};

export default MealDetailsPage;
