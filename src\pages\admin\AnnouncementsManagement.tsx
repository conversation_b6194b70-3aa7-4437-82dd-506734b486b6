import React, { useState } from 'react';
import { useAdminAnnouncements, useCreateAnnouncement, useUpdateAnnouncement, useDeleteAnnouncement } from '@/hooks/use-admin';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Calendar,
  AlertCircle,
  Info,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Megaphone,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { format } from 'date-fns';
import { Announcement, CreateAnnouncement, UpdateAnnouncement } from '@/services/admin.service';

// Content Renderer Component with truncation
const ContentRenderer = ({ content, maxLength = 300 }: { content: string; maxLength?: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Validate and sanitize content
  if (!content || typeof content !== 'string') {
    return <div className="text-gray-500 italic">No content available</div>;
  }

  // Strip HTML tags for length calculation
  const stripHtml = (html: string) => {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  const textContent = stripHtml(content);
  const shouldTruncate = textContent.length > maxLength;

  if (!shouldTruncate) {
    return (
      <div
        className="text-gray-600 mb-4 prose prose-sm max-w-none announcement-content"
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word'
        }}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  }

  return (
    <div className="text-gray-600 mb-4">
      <div
        className={`prose prose-sm max-w-none announcement-content transition-all duration-200 ${
          isExpanded ? '' : 'line-clamp-3 overflow-hidden'
        }`}
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word'
        }}
        dangerouslySetInnerHTML={{
          __html: isExpanded ? content : content.substring(0, maxLength) + '...'
        }}
      />
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="mt-2 p-0 h-auto text-blue-600 hover:text-blue-800"
      >
        {isExpanded ? (
          <>
            <ChevronUp className="h-4 w-4 mr-1" />
            Show less
          </>
        ) : (
          <>
            <ChevronDown className="h-4 w-4 mr-1" />
            Read more
          </>
        )}
      </Button>
    </div>
  );
};

const AnnouncementsManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [importanceFilter, setImportanceFilter] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<Announcement | null>(null);
  const [createContent, setCreateContent] = useState('');
  const [editContent, setEditContent] = useState('');

  // Fetch announcements with filters
  const { data, isLoading, error, refetch } = useAdminAnnouncements({
    category: categoryFilter && categoryFilter !== 'all' ? categoryFilter : undefined,
    importance: importanceFilter && importanceFilter !== 'all' ? importanceFilter : undefined,
  });

  const createMutation = useCreateAnnouncement();
  const updateMutation = useUpdateAnnouncement(editingItem?.id || '');
  const deleteMutation = useDeleteAnnouncement();

  const announcements = data?.data?.announcements || [];

  // Filter announcements by search term locally
  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesSearch = announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || announcement.category === categoryFilter;
    const matchesImportance = importanceFilter === 'all' || announcement.importance === importanceFilter;

    return matchesSearch && matchesCategory && matchesImportance;
  });

  const handleCreateSubmit = async (formData: FormData) => {
    const title = formData.get('title') as string;
    const content = createContent; // Use rich text content
    const category = formData.get('category') as 'feature' | 'event' | 'maintenance' | 'update' | 'other';
    const importance = formData.get('importance') as 'low' | 'medium' | 'high';
    const publishDate = formData.get('publishDate') as string;
    const expiryDate = formData.get('expiryDate') as string;

    if (!title || !content || !category || !importance) {
      alert('Please fill in all required fields.');
      return;
    }

    const createData = {
      title,
      content,
      category,
      importance,
      publishDate: publishDate ? new Date(publishDate).toISOString() : new Date().toISOString(),
      expiryDate: expiryDate ? new Date(expiryDate).toISOString() : null,
    };

    try {
      await createMutation.mutateAsync(createData);
      setIsCreateDialogOpen(false);
      setCreateContent(''); // Reset rich text content
      refetch();
    } catch (error) {
      console.error('Failed to create announcement:', error);
    }
  };

  const handleEditSubmit = async (formData: FormData) => {
    if (!editingItem) return;

    const title = formData.get('title') as string;
    const content = editContent; // Use rich text content
    const category = formData.get('category') as 'feature' | 'event' | 'maintenance' | 'update' | 'other';
    const importance = formData.get('importance') as 'low' | 'medium' | 'high';
    const publishDate = formData.get('publishDate') as string;
    const expiryDate = formData.get('expiryDate') as string;

    const updateData = {
      title: title || undefined,
      content: content || undefined,
      category: category || undefined,
      importance: importance || undefined,
      publishDate: publishDate ? new Date(publishDate).toISOString() : undefined,
      expiryDate: expiryDate ? new Date(expiryDate).toISOString() : null,
    };

    try {
      await updateMutation.mutateAsync(updateData);
      setIsEditDialogOpen(false);
      setEditingItem(null);
      setEditContent(''); // Reset rich text content
      refetch();
    } catch (error) {
      console.error('Failed to update announcement:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this announcement?')) {
      try {
        await deleteMutation.mutateAsync(id);
        refetch();
      } catch (error) {
        console.error('Failed to delete announcement:', error);
      }
    }
  };

  const openEditDialog = (item: Announcement) => {
    setEditingItem(item);
    setEditContent(item.content); // Set rich text content for editing
    setIsEditDialogOpen(true);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'feature': return <Info className="h-4 w-4" />;
      case 'event': return <CheckCircle className="h-4 w-4" />;
      case 'maintenance': return <AlertTriangle className="h-4 w-4" />;
      case 'update': return <XCircle className="h-4 w-4" />;
      case 'security': return <AlertTriangle className="h-4 w-4" />;
      case 'general': return <Info className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'feature': return 'bg-blue-100 text-blue-800';
      case 'event': return 'bg-green-100 text-green-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'update': return 'bg-purple-100 text-purple-800';
      case 'security': return 'bg-red-100 text-red-800';
      case 'general': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load announcements. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Global styles for announcement content */}
      <style jsx global>{`
        .announcement-content p {
          margin-bottom: 0.75rem;
          line-height: 1.6;
        }
        .announcement-content ul, .announcement-content ol {
          margin-bottom: 0.75rem;
          padding-left: 1.5rem;
        }
        .announcement-content li {
          margin-bottom: 0.25rem;
        }
        .announcement-content strong {
          font-weight: 600;
        }
        .announcement-content em {
          font-style: italic;
        }
        .announcement-content a {
          color: #0ea5e9;
          text-decoration: underline;
        }
        .announcement-content a:hover {
          color: #0284c7;
        }
        .announcement-content h1, .announcement-content h2, .announcement-content h3 {
          font-weight: 600;
          margin-bottom: 0.5rem;
          margin-top: 1rem;
        }
        .announcement-content h1 {
          font-size: 1.25rem;
        }
        .announcement-content h2 {
          font-size: 1.125rem;
        }
        .announcement-content h3 {
          font-size: 1rem;
        }
      `}</style>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Announcements Management</h1>
          <p className="text-gray-600 mt-1">Manage system announcements and notifications</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-nutrisnap-teal hover:bg-nutrisnap-teal/90">
              <Plus className="h-4 w-4 mr-2" />
              Create Announcement
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Announcement</DialogTitle>
              <DialogDescription>
                Create a new announcement to inform users about updates, events, or important information.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={(e) => {
              e.preventDefault();
              handleCreateSubmit(new FormData(e.currentTarget));
            }} className="space-y-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input id="title" name="title" required />
              </div>
              <div>
                <Label htmlFor="content">Content</Label>
                <RichTextEditor
                  value={createContent}
                  onChange={setCreateContent}
                  placeholder="Enter announcement content..."
                  className="mt-1"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select name="category" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="feature">Feature</SelectItem>
                      <SelectItem value="event">Event</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="update">Update</SelectItem>
                      <SelectItem value="security">Security</SelectItem>
                      <SelectItem value="general">General</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="importance">Importance</Label>
                  <Select name="importance" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select importance" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="publishDate">Publish Date (optional)</Label>
                  <Input id="publishDate" name="publishDate" type="datetime-local" />
                </div>
                <div>
                  <Label htmlFor="expiryDate">Expiry Date (optional)</Label>
                  <Input id="expiryDate" name="expiryDate" type="datetime-local" />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={createMutation.isPending}>
                  {createMutation.isPending ? 'Creating...' : 'Create'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search announcements..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="category">Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All categories</SelectItem>
                  <SelectItem value="feature">Feature</SelectItem>
                  <SelectItem value="event">Event</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                  <SelectItem value="update">Update</SelectItem>
                  <SelectItem value="security">Security</SelectItem>
                  <SelectItem value="general">General</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="importance">Importance</Label>
              <Select value={importanceFilter} onValueChange={setImportanceFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All importance levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All importance levels</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button variant="outline" onClick={() => {
                setSearchTerm('');
                setCategoryFilter('all');
                setImportanceFilter('all');
              }}>
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Announcements List */}
      <div className="space-y-4">
        {isLoading ? (
          [...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-full mb-4" />
                    <div className="flex space-x-2">
                      <Skeleton className="h-6 w-16" />
                      <Skeleton className="h-6 w-20" />
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredAnnouncements.length > 0 ? (
          filteredAnnouncements.map((announcement) => (
            <Card key={announcement.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold">{announcement.title}</h3>
                    </div>
                    <ContentRenderer content={announcement.content} maxLength={400} />
                    <div className="flex flex-wrap items-center gap-2 mb-3">
                      <Badge className={`${getCategoryColor(announcement.category)} flex items-center space-x-1`}>
                        {getCategoryIcon(announcement.category)}
                        <span>{announcement.category}</span>
                      </Badge>
                      <Badge className={getImportanceColor(announcement.importance)}>
                        {announcement.importance} importance
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>Created: {format(new Date(announcement.createdAt), 'MMM dd, yyyy HH:mm')}</span>
                      {announcement.publishDate && (
                        <span>Published: {format(new Date(announcement.publishDate), 'MMM dd, yyyy HH:mm')}</span>
                      )}
                      {announcement.expiryDate && (
                        <span>Expires: {format(new Date(announcement.expiryDate), 'MMM dd, yyyy HH:mm')}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditDialog(announcement)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(announcement.id)}
                      disabled={deleteMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-12">
            <Megaphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No announcements found</h3>
            <p className="text-gray-600 mb-4">Get started by creating your first announcement.</p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Announcement
            </Button>
          </div>
        )}
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Announcement</DialogTitle>
            <DialogDescription>
              Update the announcement details and settings.
            </DialogDescription>
          </DialogHeader>
          {editingItem && (
            <form onSubmit={(e) => {
              e.preventDefault();
              handleEditSubmit(new FormData(e.currentTarget));
            }} className="space-y-4">
              <div>
                <Label htmlFor="edit-title">Title</Label>
                <Input id="edit-title" name="title" defaultValue={editingItem.title} />
              </div>
              <div>
                <Label htmlFor="edit-content">Content</Label>
                <RichTextEditor
                  value={editContent}
                  onChange={setEditContent}
                  placeholder="Enter announcement content..."
                  className="mt-1"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-category">Category</Label>
                  <Select name="category" defaultValue={editingItem.category}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="feature">Feature</SelectItem>
                      <SelectItem value="event">Event</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="update">Update</SelectItem>
                      <SelectItem value="security">Security</SelectItem>
                      <SelectItem value="general">General</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-importance">Importance</Label>
                  <Select name="importance" defaultValue={editingItem.importance}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-publishDate">Publish Date (optional)</Label>
                  <Input
                    id="edit-publishDate"
                    name="publishDate"
                    type="datetime-local"
                    defaultValue={editingItem.publishDate ? new Date(editingItem.publishDate).toISOString().slice(0, 16) : ''}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-expiryDate">Expiry Date (optional)</Label>
                  <Input
                    id="edit-expiryDate"
                    name="expiryDate"
                    type="datetime-local"
                    defaultValue={editingItem.expiryDate ? new Date(editingItem.expiryDate).toISOString().slice(0, 16) : ''}
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => {
                  setIsEditDialogOpen(false);
                  setEditingItem(null);
                }}>
                  Cancel
                </Button>
                <Button type="submit" disabled={updateMutation.isPending}>
                  {updateMutation.isPending ? 'Updating...' : 'Update'}
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AnnouncementsManagement;
