
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import Navbar from "@/components/layout/Navbar";
import { Camera, ArrowRight } from "lucide-react";
import { useActiveAnnouncements } from "@/hooks/use-announcements";
import { Announcement } from "@/services/announcements.service";
import GalleryService, { PublicGalleryItem } from "@/services/gallery.service";
import { getImageUrl } from "@/lib/image-utils";
import VisitorCounter from "@/components/VisitorCounter";
import OnlineUsersButton from "@/components/OnlineUsersButton";

const Index = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [featuredGallery, setFeaturedGallery] = useState<PublicGalleryItem[]>([]);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoadingGallery, setIsLoadingGallery] = useState(true);

  // Fetch announcements from API
  const { data: announcementsData, isLoading: isLoadingAnnouncements } = useActiveAnnouncements();

  // Fetch ONLY featured gallery items for landing page (all images are shown in /admin/gallery)
  useEffect(() => {
    const fetchFeaturedGallery = async () => {
      try {
        setIsLoadingGallery(true);
        console.log('🔍 Fetching ONLY featured gallery items for landing page...');
        const featuredItems = await GalleryService.getFeaturedItems(6); // Get 6 featured items only
        console.log('🔍 Featured gallery items received:', featuredItems);
        console.log('🔍 Featured gallery items count:', featuredItems.length);
        setFeaturedGallery(featuredItems);
      } catch (error) {
        console.error('❌ Failed to fetch featured gallery items:', error);
        console.log('🔍 Trying fallback: fetching regular gallery items with featured filter...');

        // Fallback: try to get featured items using the regular gallery endpoint
        try {
          const fallbackResponse = await GalleryService.getGalleryItems({
            featured: true,
            limit: 6
          });
          console.log('🔍 Fallback gallery response:', fallbackResponse);
          if (fallbackResponse.success && fallbackResponse.data) {
            setFeaturedGallery(fallbackResponse.data);
            console.log('✅ Fallback successful, got', fallbackResponse.data.length, 'items');
          } else {
            setFeaturedGallery([]);
          }
        } catch (fallbackError) {
          console.error('❌ Fallback also failed:', fallbackError);
          setFeaturedGallery([]);
        }
      } finally {
        setIsLoadingGallery(false);
      }
    };

    fetchFeaturedGallery();
  }, []);

  // Update announcements when data is fetched
  useEffect(() => {
    if (announcementsData?.announcements) {
      // Sort announcements by importance and date
      const sorted = [...announcementsData.announcements].sort((a, b) => {
        const importanceOrder = { high: 3, medium: 2, low: 1 };
        const aImportance = importanceOrder[a.importance as keyof typeof importanceOrder] || 0;
        const bImportance = importanceOrder[b.importance as keyof typeof importanceOrder] || 0;

        if (aImportance !== bImportance) return bImportance - aImportance;

        return new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime();
      });

      setAnnouncements(sorted);
    }
  }, [announcementsData]);

  const handleSnapNow = () => {
    if (isAuthenticated) {
      // If user is admin, redirect to admin dashboard instead of snap
      if (user?.role === 'admin') {
        navigate("/admin/dashboard");
      } else {
        // Regular users go to snap page
        navigate("/snap-new");
      }
    } else {
      // Not authenticated - trigger the login modal
      document.dispatchEvent(new Event("open-login-modal"));
    }
  };

  const handleLearnMore = () => {
    // Scroll to "Ready to start your nutrition journey?" section
    const ctaSection = document.getElementById('cta-section');
    if (ctaSection) {
      ctaSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-nutrisnap-teal/10 to-nutrisnap-teal/5 py-16 md:py-24 relative">
        {/* Online Users Button - Top Right */}
        <div className="absolute top-4 right-4 z-10">
          <OnlineUsersButton />
        </div>

        <div className="container px-4 md:px-6 mx-auto grid gap-8 md:grid-cols-2 items-center">
          <div className="space-y-6 animate-fade-in">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-nutrisnap-charcoal">
              Track your food with just a picture
            </h1>
            <p className="text-lg md:text-xl text-gray-600 max-w-xl">
              NutriSnap uses AI to instantly analyze your meals, provide nutritional insights, and help you maintain a healthy diet.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Hide Snap button for admin users */}
              {user?.role !== 'admin' && (
                <Button
                  size="lg"
                  className="bg-nutrisnap-teal hover:bg-nutrisnap-teal/90 text-white"
                  onClick={handleSnapNow}
                >
                  <Camera className="mr-2 h-5 w-5" />
                  Snap Now
                </Button>
              )}
              {!isAuthenticated && (
                <Button
                  variant="outline"
                  size="lg"
                  className="border-nutrisnap-teal text-nutrisnap-teal hover:bg-nutrisnap-teal/10"
                  onClick={handleLearnMore}
                >
                  Learn More
                </Button>
              )}
            </div>
          </div>
          <div className="relative">
            <div className="relative z-10 ml-6 animate-scale-in">
              <img
                src="https://images.unsplash.com/photo-1482049016688-2d3e1b311543?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80"
                alt="Food tracking app"
                className="rounded-lg shadow-xl"
              />
            </div>
            <div className="absolute -bottom-4 -left-4 w-3/4 h-3/4 bg-nutrisnap-orange/20 rounded-lg -z-10"></div>
          </div>
        </div>
      </section>

      {/* Visitor Counter Section */}
      <section className="py-12 bg-white">
        <div className="container px-4 md:px-6 mx-auto">
          <VisitorCounter className="max-w-4xl mx-auto" />
        </div>
      </section>

      {/* Announcements Section */}
      <section id="latest-updates" className="py-16 bg-white">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-nutrisnap-charcoal">Latest Updates</h2>
            <Button
              variant="ghost"
              className="text-nutrisnap-teal hover:text-nutrisnap-teal/90"
              onClick={() => navigate("/announcements")}
            >
              View All <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>

          {isLoadingAnnouncements ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-nutrisnap-teal"></div>
            </div>
          ) : announcements.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-gray-500">No announcements available at this time.</p>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {announcements.slice(0, 3).map((announcement) => (
                <div
                  key={announcement.id}
                  className="bg-white border border-gray-100 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                >
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium uppercase ${
                        announcement.importance === "high"
                          ? "bg-red-100 text-red-800"
                          : announcement.importance === "medium"
                          ? "bg-amber-100 text-amber-800"
                          : "bg-blue-100 text-blue-800"
                      }`}>
                        {announcement.category}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(announcement.publishDate).toLocaleDateString()}
                      </span>
                    </div>
                    <h3 className="text-lg font-semibold mb-2">{announcement.title}</h3>
                    <div
                      className="text-gray-600 line-clamp-3"
                      dangerouslySetInnerHTML={{ __html: announcement.content }}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Gallery Section */}
      <section id="featured-gallery" className="py-16 bg-gray-50">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-nutrisnap-charcoal">Featured Gallery</h2>
            <Button
              variant="ghost"
              className="text-nutrisnap-teal hover:text-nutrisnap-teal/90"
              onClick={() => navigate("/gallery")}
            >
              View Gallery <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
          {isLoadingGallery ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg overflow-hidden shadow-sm">
                  <div className="h-48 bg-gray-200 animate-pulse"></div>
                  <div className="p-6">
                    <div className="h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : featuredGallery.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {featuredGallery.map((item) => (
                <div
                  key={item.id}
                  className="group bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={getImageUrl(item.imageUrl)}
                      alt={item.title}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTJhNSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pgo8L3N2Zz4K';
                      }}
                    />
                    {item.featured && (
                      <div className="absolute top-2 right-2">
                        <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                          Featured
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-semibold mb-2">{item.title}</h3>
                    <p className="text-gray-600 line-clamp-2">{item.description || 'No description available'}</p>
                    <div className="mt-3">
                      <span className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                        {item.category}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Camera className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Featured Images</h3>
              <p className="text-gray-600">Check back later for featured gallery items.</p>
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section id="cta-section" className="py-16 bg-nutrisnap-teal/10">
        <div className="container px-4 md:px-6 mx-auto text-center">
          <div className="max-w-3xl mx-auto space-y-6">
            <h2 className="text-3xl md:text-4xl font-bold text-nutrisnap-charcoal">
              Ready to start your nutrition journey?
            </h2>
            <p className="text-lg text-gray-600">
              Take the first step towards a healthier lifestyle with NutriSnap's AI-powered food tracking.
            </p>
            {/* Hide Snap button for admin users */}
            {user?.role !== 'admin' && (
              <Button
                size="lg"
                className="bg-nutrisnap-teal hover:bg-nutrisnap-teal/90 text-white"
                onClick={handleSnapNow}
              >
                <Camera className="mr-2 h-5 w-5" />
                Snap Your First Meal
              </Button>
            )}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-nutrisnap-charcoal text-white py-12">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <div>
              <div className="flex items-center mb-4">
                <Camera className="h-6 w-6 text-nutrisnap-teal" />
                <span className="ml-2 text-xl font-bold">NutriSnap</span>
              </div>
              <p className="text-gray-400">
                Track your nutrition with AI-powered food recognition
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Features</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white">Food Tracking</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Nutritional Insights</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Meal History</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Progress Tracking</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Resources</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white">Blog</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Nutrition Guide</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">FAQ</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Support</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white">Privacy Policy</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Terms of Service</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white">Cookie Policy</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-8 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-gray-400 text-sm">
                &copy; {new Date().getFullYear()} NutriSnap. All rights reserved.
              </div>
              <VisitorCounter compact className="flex-shrink-0" showTitle={false} />
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
