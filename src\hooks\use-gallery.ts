import { useQuery } from '@tanstack/react-query';
import GalleryService from '@/services/gallery.service';

// Public Gallery Hooks
export const useGalleryItems = (params?: {
  page?: number;
  limit?: number;
  category?: string;
  featured?: boolean;
  search?: string;
}) => {
  return useQuery({
    queryKey: ['gallery', params],
    queryFn: () => GalleryService.getGalleryItems(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useFeaturedGalleryItems = (limit?: number) => {
  return useQuery({
    queryKey: ['gallery', 'featured', limit],
    queryFn: () => GalleryService.getFeaturedItems(limit),
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};

export const useGalleryCategories = () => {
  return useQuery({
    queryKey: ['gallery', 'categories'],
    queryFn: GalleryService.getCategories,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

export const useGalleryItemBySlug = (slug: string) => {
  return useQuery({
    queryKey: ['gallery', 'item', slug],
    queryFn: () => GalleryService.getItemBySlug(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
