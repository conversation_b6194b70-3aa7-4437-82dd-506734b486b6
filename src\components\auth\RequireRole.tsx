import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Shield, AlertCircle } from 'lucide-react';

interface RequireRoleProps {
  children: React.ReactNode;
  role: 'admin' | 'editor';
  fallback?: React.ReactNode;
}

const RequireRole: React.FC<RequireRoleProps> = ({ children, role, fallback }) => {
  const { user } = useAuth();

  // Check if user has the required role
  const hasRequiredRole = () => {
    if (!user) return false;

    // Check if user can access admin panel at all
    if (!user.canAccessAdminPanel) return false;

    if (role === 'admin') {
      return user.role === 'admin';
    }

    if (role === 'editor') {
      return user.role === 'editor' || user.role === 'admin';
    }

    return false;
  };

  if (!hasRequiredRole()) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            You don't have permission to access this page. {role === 'admin' ? 'Administrator' : 'Editor'} access is required.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return <>{children}</>;
};

export default RequireRole;
