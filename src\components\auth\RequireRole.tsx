import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Shield, AlertCircle } from 'lucide-react';

interface RequireRoleProps {
  children: React.ReactNode;
  role: 'admin' | 'editor' | 'user';
  allowRegularUsers?: boolean; // Allow regular users (non-admin/editor)
  fallback?: React.ReactNode;
}

const RequireRole: React.FC<RequireRoleProps> = ({ children, role, allowRegularUsers, fallback }) => {
  const { user } = useAuth();

  // Check if user has the required role
  const hasRequiredRole = () => {
    if (!user) return false;

    // For regular user routes (personal nutrition features)
    if (role === 'user' && allowRegularUsers) {
      // Allow regular users (non-admin/editor) only
      return user.role === 'user' || !user.role || (!user.canAccessAdminPanel);
    }

    // For admin panel routes
    if (!user.canAccessAdminPanel) return false;

    if (role === 'admin') {
      return user.role === 'admin';
    }

    if (role === 'editor') {
      return user.role === 'editor' || user.role === 'admin';
    }

    return false;
  };

  if (!hasRequiredRole()) {
    if (fallback) {
      return <>{fallback}</>;
    }

    const getAccessMessage = () => {
      if (role === 'user' && allowRegularUsers) {
        return 'This feature is only available for regular users. Administrators and editors should use the admin panel.';
      }
      return `You don't have permission to access this page. ${role === 'admin' ? 'Administrator' : 'Editor'} access is required.`;
    };

    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            {getAccessMessage()}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return <>{children}</>;
};

export default RequireRole;
