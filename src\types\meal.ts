
export interface FoodItem {
  foodItem: string;
  confidence: number;
  boundingBox: { x: number; y: number; width: number; height: number };
  quantityGrams: number;
  commonPortions: string[];
  selectedPortion: string;
  userVerified: boolean;
  userAdded?: boolean;
}

export interface FoodAnalysis {
  id?: string;
  _id?: string;
  imageUrl: string;
  userNotes: string;
  mealCategory: string;
  mealDateTime: string;
  recognitionResults: FoodItem[];
  nutritionalSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
  };
}
