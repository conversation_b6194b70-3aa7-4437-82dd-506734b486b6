import apiClient from '@/lib/api-client';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface User {
  id?: string;
  _id?: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'user' | 'editor' | 'admin';
  isAdmin: boolean; // Keep for backward compatibility
  isActive?: boolean; // User active status
  canAccessAdminPanel: boolean; // New field from backend
  lastLogin?: string;
  createdAt?: string;
  preferences?: {
    dietaryRestrictions: string[];
    nutritionGoals: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
    };
    units: 'metric' | 'imperial';
  };
}

export interface AuthResponse {
  success: boolean;
  token: string;
  user: User;
}

const AuthService = {
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
    return response.data;
  },

  register: async (userData: RegisterData): Promise<AuthResponse> => {
    const response = await apiClient.post<AuthResponse>('/auth/register', userData);
    return response.data;
  },

  getProfile: async (): Promise<{ success: boolean; user: User }> => {
    const response = await apiClient.get<{ success: boolean; user: User }>('/users/profile');
    return response.data;
  },

  updatePreferences: async (preferences: {
    dietaryRestrictions?: string[];
    nutritionGoals?: {
      calories?: number;
      protein?: number;
      carbs?: number;
      fat?: number;
    };
    units?: 'metric' | 'imperial';
  }): Promise<{ success: boolean; user: User }> => {
    const response = await apiClient.put<{ success: boolean; user: User }>(
      '/users/preferences',
      preferences
    );
    return response.data;
  },
};

export default AuthService;
