import apiClient from '@/lib/api-client';

// Visitor Count Types
export interface VisitorStats {
  onlineNow: number;
  todayVisitors: number;
  weeklyVisitors: number;
  totalVisitors: number;
  lastUpdated: string;
}

export interface VisitorResponse {
  success: boolean;
  data: VisitorStats;
  message?: string;
}

// Visitor Service
const VisitorService = {
  // Get current visitor statistics (public endpoint)
  getVisitorStats: async (): Promise<VisitorStats> => {
    try {
      const response = await apiClient.get<VisitorResponse>('/visitor-count');
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch visitor stats:', error);
      // Return fallback data if API fails
      return {
        onlineNow: 0,
        todayVisitors: 0,
        weeklyVisitors: 0,
        totalVisitors: 0,
        lastUpdated: new Date().toISOString()
      };
    }
  },

  // Track visitor (automatically called when user visits)
  trackVisitor: async (): Promise<void> => {
    try {
      await apiClient.post('/visitor-track');
    } catch (error) {
      // Silently fail - visitor tracking is not critical
      console.debug('Visitor tracking failed:', error);
    }
  }
};

export default VisitorService;
