import apiClient from '@/lib/api-client';

export interface FoodItem {
  id: string;
  name: string;
  category: string;
  nutrientsPerServing: {
    servingSize: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
    vitamin_a?: number;
    vitamin_c?: number;
    calcium?: number;
    iron?: number;
  };
  commonPortions: string[];
}

export interface FoodDatabaseResponse {
  success: boolean;
  count: number;
  total: number;
  pages: number;
  page: number;
  foodItems: FoodItem[];
}

export interface FoodItemResponse {
  success: boolean;
  foodItem: FoodItem;
}

export interface CategoriesResponse {
  success: boolean;
  categories: string[];
}

const FoodDatabaseService = {
  // Search food items
  searchFoods: async (params?: {
    search?: string;
    category?: string;
    limit?: number;
    page?: number;
  }): Promise<FoodDatabaseResponse> => {
    const response = await apiClient.get<FoodDatabaseResponse>('/food-database', {
      params,
    });
    return response.data;
  },

  // Get food categories
  getCategories: async (): Promise<CategoriesResponse> => {
    const response = await apiClient.get<CategoriesResponse>('/food-database/categories');
    return response.data;
  },

  // Get food item by ID
  getFoodById: async (id: string): Promise<FoodItemResponse> => {
    const response = await apiClient.get<FoodItemResponse>(`/food-database/${id}`);
    return response.data;
  },

  // Create a new food item (admin only)
  createFood: async (data: Omit<FoodItem, 'id'>): Promise<FoodItemResponse> => {
    const response = await apiClient.post<FoodItemResponse>('/food-database', data);
    return response.data;
  },

  // Update a food item (admin only)
  updateFood: async (
    id: string,
    data: Partial<Omit<FoodItem, 'id'>>
  ): Promise<FoodItemResponse> => {
    const response = await apiClient.put<FoodItemResponse>(`/food-database/${id}`, data);
    return response.data;
  },

  // Delete a food item (admin only)
  deleteFood: async (id: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete<{ success: boolean; message: string }>(
      `/food-database/${id}`
    );
    return response.data;
  },
};

export default FoodDatabaseService;
