import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  Info, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  Megaphone 
} from 'lucide-react';
import { format } from 'date-fns';
import AnnouncementsService, { PublicAnnouncement } from '@/services/announcements.service';

const Announcements = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [announcements, setAnnouncements] = useState<PublicAnnouncement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [categoryFilter, setCategoryFilter] = useState(searchParams.get('category') || 'all');
  const [importanceFilter, setImportanceFilter] = useState(searchParams.get('importance') || 'all');
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get('page') || '1'));
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 10;

  // Categories for filtering
  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'feature', label: 'Features' },
    { value: 'event', label: 'Events' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'update', label: 'Updates' },
    { value: 'other', label: 'Other' },
  ];

  const importanceLevels = [
    { value: 'all', label: 'All Levels' },
    { value: 'high', label: 'High' },
    { value: 'medium', label: 'Medium' },
    { value: 'low', label: 'Low' },
  ];

  // Fetch announcements
  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: currentPage,
        limit: itemsPerPage,
        ...(categoryFilter !== 'all' && { category: categoryFilter }),
        ...(importanceFilter !== 'all' && { importance: importanceFilter }),
      };

      const response = await AnnouncementsService.getActiveAnnouncements(params);
      
      setAnnouncements(response.announcements || []);
      if (response.pagination) {
        setTotalPages(response.pagination.pages || 1);
        setTotalItems(response.pagination.total || 0);
      }
    } catch (err) {
      console.error('Error fetching announcements:', err);
      setError('Failed to load announcements');
    } finally {
      setLoading(false);
    }
  };

  // Filter announcements locally by search term
  const filteredAnnouncements = announcements.filter(announcement =>
    announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    announcement.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Update URL params when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    if (searchTerm) params.set('search', searchTerm);
    if (categoryFilter !== 'all') params.set('category', categoryFilter);
    if (importanceFilter !== 'all') params.set('importance', importanceFilter);
    if (currentPage > 1) params.set('page', currentPage.toString());
    
    setSearchParams(params);
  }, [searchTerm, categoryFilter, importanceFilter, currentPage, setSearchParams]);

  // Fetch data when filters change
  useEffect(() => {
    fetchAnnouncements();
  }, [currentPage, categoryFilter, importanceFilter]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled locally, no need to refetch
  };

  // Clear filters
  const clearFilters = () => {
    setSearchTerm('');
    setCategoryFilter('all');
    setImportanceFilter('all');
    setCurrentPage(1);
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'feature': return <Info className="h-4 w-4" />;
      case 'event': return <CheckCircle className="h-4 w-4" />;
      case 'maintenance': return <AlertTriangle className="h-4 w-4" />;
      case 'update': return <XCircle className="h-4 w-4" />;
      case 'other': return <Info className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  // Get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'feature': return 'bg-blue-100 text-blue-800';
      case 'event': return 'bg-green-100 text-green-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'update': return 'bg-purple-100 text-purple-800';
      case 'other': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get importance color
  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Global styles for announcement content */}
      <style jsx global>{`
        .announcement-content p {
          margin-bottom: 0.75rem;
          line-height: 1.6;
        }
        .announcement-content ul, .announcement-content ol {
          margin-bottom: 0.75rem;
          padding-left: 1.5rem;
        }
        .announcement-content li {
          margin-bottom: 0.25rem;
        }
        .announcement-content strong {
          font-weight: 600;
        }
        .announcement-content em {
          font-style: italic;
        }
        .announcement-content a {
          color: #0ea5e9;
          text-decoration: underline;
        }
        .announcement-content a:hover {
          color: #0284c7;
        }
        .announcement-content h1, .announcement-content h2, .announcement-content h3 {
          font-weight: 600;
          margin-bottom: 0.5rem;
          margin-top: 1rem;
        }
        .announcement-content h1 {
          font-size: 1.25rem;
        }
        .announcement-content h2 {
          font-size: 1.125rem;
        }
        .announcement-content h3 {
          font-size: 1rem;
        }
      `}</style>

      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Announcements</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Stay updated with the latest news, features, and important information
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-end">
              {/* Search */}
              <div className="flex-1">
                <Label htmlFor="search">Search</Label>
                <form onSubmit={handleSearch} className="flex gap-2 mt-1">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search announcements..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </form>
              </div>

              {/* Category Filter */}
              <div className="w-full lg:w-48">
                <Label>Category</Label>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Importance Filter */}
              <div className="w-full lg:w-48">
                <Label>Importance</Label>
                <Select value={importanceFilter} onValueChange={setImportanceFilter}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {importanceLevels.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Clear Filters */}
              <Button variant="outline" onClick={clearFilters}>
                <Filter className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results Info */}
        <div className="flex justify-between items-center mb-6">
          <p className="text-gray-600">
            {loading ? 'Loading...' : `${filteredAnnouncements.length} announcements found`}
          </p>
          {totalPages > 1 && (
            <p className="text-gray-600">
              Page {currentPage} of {totalPages}
            </p>
          )}
        </div>

        {/* Announcements List */}
        {loading ? (
          <div className="space-y-6">
            {[...Array(5)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <Skeleton className="h-6 w-3/4 mb-2" />
                      <div className="flex gap-2">
                        <Skeleton className="h-5 w-16" />
                        <Skeleton className="h-5 w-20" />
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-2/3 mb-4" />
                  <Skeleton className="h-3 w-1/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredAnnouncements.length > 0 ? (
          <div className="space-y-6">
            {filteredAnnouncements.map((announcement) => (
              <Card key={announcement.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-xl mb-3">{announcement.title}</CardTitle>
                      <div className="flex flex-wrap gap-2">
                        <Badge className={`${getCategoryColor(announcement.category)} flex items-center space-x-1`}>
                          {getCategoryIcon(announcement.category)}
                          <span>{announcement.category}</span>
                        </Badge>
                        <Badge className={getImportanceColor(announcement.importance)}>
                          {announcement.importance} importance
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div
                    className="text-gray-700 mb-4 prose prose-sm max-w-none announcement-content"
                    style={{
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word'
                    }}
                    dangerouslySetInnerHTML={{ __html: announcement.content }}
                  />
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        Published: {format(new Date(announcement.publishDate), 'MMM dd, yyyy')}
                      </div>
                      {announcement.expiryDate && (
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          Expires: {format(new Date(announcement.expiryDate), 'MMM dd, yyyy')}
                        </div>
                      )}
                      {!announcement.expiryDate && (
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          No expiry date
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Megaphone className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No announcements found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filters.</p>
            <Button onClick={clearFilters}>Clear Filters</Button>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex gap-2">
              <Button
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                Previous
              </Button>
              {[...Array(Math.min(5, totalPages))].map((_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? 'default' : 'outline'}
                    onClick={() => setCurrentPage(page)}
                  >
                    {page}
                  </Button>
                );
              })}
              <Button
                variant="outline"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Announcements;
