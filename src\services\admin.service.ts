import apiClient from '@/lib/api-client';
import { User } from './auth.service';

// Admin Dashboard Types
export interface DashboardStats {
  totalUsers: number;
  totalFoodAnalyses: number;
  totalGalleryItems: number;
  totalAnnouncements: number;
  recentActivity: ActivityItem[];
  userGrowth: GrowthData[];
  popularFoods: PopularFood[];
}

export interface ActivityItem {
  id: string;
  type: 'user_registration' | 'food_analysis' | 'gallery_upload' | 'announcement';
  description: string;
  timestamp: string;
  user?: {
    id: string;
    name: string;
  };
}

export interface GrowthData {
  date: string;
  users: number;
  analyses: number;
}

export interface PopularFood {
  name: string;
  count: number;
  percentage: number;
}

// Gallery Types
export interface GalleryItem {
  id: string;
  title: string;
  description?: string;
  imageUrl: string;
  category: string;
  slug: string;
  featured: boolean;
  tags: string[];
  uploadedBy: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateGalleryItem {
  title: string;
  description?: string;
  category: string;
  featured?: boolean;
  tags?: string[];
  image: File;
}

export interface UpdateGalleryItem {
  title?: string;
  description?: string;
  category?: string;
  featured?: boolean;
  tags?: string[];
  image?: File;
}

// Announcement Types
export interface Announcement {
  id: string;
  title: string;
  content: string;
  category: 'feature' | 'event' | 'maintenance' | 'update' | 'other';
  importance: 'low' | 'medium' | 'high';
  publishDate: string;
  expiryDate: string;
  createdAt: string;
}

export interface CreateAnnouncement {
  title: string;
  content: string;
  category: 'feature' | 'event' | 'maintenance' | 'update' | 'other';
  importance: 'low' | 'medium' | 'high';
  publishDate?: string;
  expiryDate: string;
}

export interface UpdateAnnouncement {
  title?: string;
  content?: string;
  category?: 'feature' | 'event' | 'maintenance' | 'update' | 'other';
  importance?: 'low' | 'medium' | 'high';
  publishDate?: string;
  expiryDate?: string;
}

// User Management Types
export interface AdminUser extends User {
  id?: string;
  _id?: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLoginIp?: string;
  loginCount: number;
}

export interface CreateUser {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'user' | 'editor' | 'admin';
}

export interface UpdateUser {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: 'user' | 'editor' | 'admin';
  status?: 'active' | 'inactive' | 'suspended';
}

// System Configuration Types
export interface SystemConfig {
  key: string;
  value: any;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'object';
  category: string;
  isPublic: boolean;
  updatedBy?: {
    id: string;
    name: string;
  };
  updatedAt: string;
}

export interface CreateSystemConfig {
  key: string;
  value: any;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'object';
  category: string;
  isPublic?: boolean;
}

// System Logs Types
export interface SystemLog {
  id: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  category: string;
  userId?: string;
  userEmail?: string;
  ip?: string;
  userAgent?: string;
  metadata?: any;
  timestamp: string;
}

export interface LogStats {
  totalLogs: number;
  errorCount: number;
  warningCount: number;
  infoCount: number;
  debugCount: number;
  recentErrors: SystemLog[];
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    users?: T[];
    galleryItems?: T[];
    announcements?: T[];
    logs?: T[];
    pagination: {
      current: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

// Admin Service Implementation
const AdminService = {
  // Dashboard & Analytics
  getDashboard: async (): Promise<DashboardStats> => {
    const response = await apiClient.get<ApiResponse<DashboardStats>>('/admin/dashboard');
    return response.data.data;
  },

  getAnalytics: async (): Promise<any> => {
    const response = await apiClient.get<ApiResponse<any>>('/admin/analytics');
    return response.data.data;
  },

  getSystemInfo: async (): Promise<any> => {
    const response = await apiClient.get<ApiResponse<any>>('/admin/system-info');
    return response.data.data;
  },

  // User Management
  getUsers: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    status?: string;
  }): Promise<PaginatedResponse<AdminUser>> => {
    const response = await apiClient.get<PaginatedResponse<AdminUser>>('/admin/users', { params });
    return response.data;
  },

  getUserById: async (id: string): Promise<AdminUser> => {
    const response = await apiClient.get<ApiResponse<AdminUser>>(`/admin/users/${id}`);
    return response.data.data;
  },

  createUser: async (userData: CreateUser): Promise<AdminUser> => {
    const response = await apiClient.post<ApiResponse<AdminUser>>('/admin/users', userData);
    return response.data.data;
  },

  updateUser: async (id: string, userData: UpdateUser): Promise<AdminUser> => {
    const response = await apiClient.put<ApiResponse<AdminUser>>(`/admin/users/${id}`, userData);
    return response.data.data;
  },

  deleteUser: async (id: string): Promise<void> => {
    await apiClient.delete(`/admin/users/${id}`);
  },

  // Gallery Management
  getGalleryItems: async (params?: {
    page?: number;
    limit?: number;
    category?: string;
    featured?: boolean;
    search?: string;
  }): Promise<PaginatedResponse<GalleryItem>> => {
    const response = await apiClient.get<{
      success: boolean;
      data: {
        galleryItems: (GalleryItem & {
          _id: string;
          isFeatured: boolean;
          tags: string[];
          uploadedBy: {
            _id: string;
            firstName: string;
            lastName: string;
            email: string
          }
        })[];
        pagination: {
          current: number;
          limit: number;
          total: number;
          pages: number;
        };
      };
    }>('/admin/gallery', { params });

    // Transform the response to match expected structure and map _id to id
    const transformedGalleryItems = response.data.data.galleryItems.map(item => {
      // Parse tags if they're JSON strings
      let parsedTags = item.tags || [];
      if (Array.isArray(parsedTags) && parsedTags.length > 0) {
        try {
          // Handle tags that might be JSON strings
          parsedTags = parsedTags.flatMap(tag => {
            if (typeof tag === 'string' && tag.startsWith('[')) {
              return JSON.parse(tag);
            }
            return tag;
          }).filter(tag => tag && tag !== '[]');
        } catch (e) {
          console.warn('Failed to parse tags:', item.tags);
          parsedTags = [];
        }
      }

      return {
        ...item,
        id: item._id,
        featured: item.isFeatured,
        tags: parsedTags,
        uploadedBy: {
          id: item.uploadedBy._id,
          name: `${item.uploadedBy.firstName} ${item.uploadedBy.lastName}`
        }
      };
    });

    return {
      success: response.data.success,
      data: {
        galleryItems: transformedGalleryItems,
        pagination: response.data.data.pagination
      }
    };
  },

  getGalleryItem: async (id: string): Promise<GalleryItem> => {
    const response = await apiClient.get<{
      success: boolean;
      data: GalleryItem & { _id: string; isFeatured: boolean; uploadedBy: { _id: string; firstName: string; lastName: string; email: string } };
    }>(`/admin/gallery/${id}`);

    const item = response.data.data;
    return {
      ...item,
      id: item._id,
      featured: item.isFeatured,
      uploadedBy: {
        id: item.uploadedBy._id,
        name: `${item.uploadedBy.firstName} ${item.uploadedBy.lastName}`
      }
    };
  },

  createGalleryItem: async (data: CreateGalleryItem): Promise<GalleryItem> => {
    const formData = new FormData();
    formData.append('title', data.title);
    if (data.description) formData.append('description', data.description);
    formData.append('category', data.category);
    if (data.featured !== undefined) formData.append('isFeatured', data.featured.toString());
    if (data.tags) formData.append('tags', data.tags.join(','));
    formData.append('image', data.image);

    const response = await apiClient.post<{
      success: boolean;
      data: {
        galleryItem: GalleryItem & {
          _id: string;
          isFeatured: boolean;
          uploadedBy: { _id: string; firstName: string; lastName: string; email: string }
        }
      };
    }>('/admin/gallery', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });

    const item = response.data.data.galleryItem;
    return {
      ...item,
      id: item._id,
      featured: item.isFeatured,
      uploadedBy: {
        id: item.uploadedBy._id,
        name: `${item.uploadedBy.firstName} ${item.uploadedBy.lastName}`
      }
    };
  },

  updateGalleryItem: async (id: string, data: UpdateGalleryItem): Promise<GalleryItem> => {
    const formData = new FormData();
    if (data.title) formData.append('title', data.title);
    if (data.description) formData.append('description', data.description);
    if (data.category) formData.append('category', data.category);
    if (data.featured !== undefined) formData.append('isFeatured', data.featured.toString());
    if (data.tags) formData.append('tags', data.tags.join(','));
    if (data.image) formData.append('image', data.image);

    const response = await apiClient.put<{
      success: boolean;
      data: {
        galleryItem: GalleryItem & {
          _id: string;
          isFeatured: boolean;
          uploadedBy: { _id: string; firstName: string; lastName: string; email: string }
        }
      };
    }>(`/admin/gallery/${id}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });

    const item = response.data.data.galleryItem;
    return {
      ...item,
      id: item._id,
      featured: item.isFeatured,
      uploadedBy: {
        id: item.uploadedBy._id,
        name: `${item.uploadedBy.firstName} ${item.uploadedBy.lastName}`
      }
    };
  },

  deleteGalleryItem: async (id: string): Promise<void> => {
    await apiClient.delete(`/admin/gallery/${id}`);
  },

  // Announcements Management
  getAllAnnouncements: async (params?: {
    page?: number;
    limit?: number;
    active?: boolean;
    type?: string;
    priority?: string;
  }): Promise<PaginatedResponse<Announcement>> => {
    const response = await apiClient.get<{
      success: boolean;
      count: number;
      total: number;
      announcements: (Announcement & { _id: string })[];
      pagination: {
        current: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>('/admin/announcements', { params });

    // Transform the response to match PaginatedResponse structure and map _id to id
    const transformedAnnouncements = response.data.announcements.map(announcement => ({
      ...announcement,
      id: announcement._id
    }));

    return {
      success: response.data.success,
      data: {
        announcements: transformedAnnouncements,
        pagination: response.data.pagination
      }
    };
  },

  getAnnouncement: async (id: string): Promise<Announcement> => {
    const response = await apiClient.get<{
      success: boolean;
      announcement: Announcement & { _id: string };
    }>(`/admin/announcements/${id}`);
    return {
      ...response.data.announcement,
      id: response.data.announcement._id
    };
  },

  createAnnouncement: async (data: CreateAnnouncement): Promise<Announcement> => {
    const response = await apiClient.post<{
      success: boolean;
      announcement: Announcement & { _id: string };
    }>('/admin/announcements', data);
    return {
      ...response.data.announcement,
      id: response.data.announcement._id
    };
  },

  updateAnnouncement: async (id: string, data: UpdateAnnouncement): Promise<Announcement> => {
    const response = await apiClient.put<{
      success: boolean;
      announcement: Announcement & { _id: string };
    }>(`/admin/announcements/${id}`, data);
    return {
      ...response.data.announcement,
      id: response.data.announcement._id
    };
  },

  deleteAnnouncement: async (id: string): Promise<void> => {
    await apiClient.delete(`/admin/announcements/${id}`);
  },

  // System Configuration
  getConfigurations: async (params?: {
    category?: string;
    isPublic?: boolean;
  }): Promise<SystemConfig[]> => {
    const response = await apiClient.get<ApiResponse<{ configs: Record<string, SystemConfig[]>; total: number }>>('/admin/config', { params });

    // Flatten the grouped configs into a single array
    const groupedConfigs = response.data.data.configs;
    const flatConfigs: SystemConfig[] = [];

    Object.values(groupedConfigs).forEach(categoryConfigs => {
      flatConfigs.push(...categoryConfigs);
    });

    return flatConfigs;
  },

  getConfiguration: async (key: string): Promise<SystemConfig> => {
    const response = await apiClient.get<ApiResponse<SystemConfig>>(`/admin/config/${key}`);
    return response.data.data;
  },

  createConfiguration: async (data: CreateSystemConfig): Promise<SystemConfig> => {
    const response = await apiClient.post<ApiResponse<SystemConfig>>('/admin/config', data);
    return response.data.data;
  },

  updateConfiguration: async (key: string, data: Partial<CreateSystemConfig>): Promise<SystemConfig> => {
    const response = await apiClient.put<ApiResponse<SystemConfig>>(`/admin/config/${key}`, data);
    return response.data.data;
  },

  deleteConfiguration: async (key: string): Promise<void> => {
    await apiClient.delete(`/admin/config/${key}`);
  },

  // System Logs
  getLogs: async (params?: {
    page?: number;
    limit?: number;
    level?: string;
    category?: string;
    startDate?: string;
    endDate?: string;
    userId?: string;
  }): Promise<PaginatedResponse<SystemLog>> => {
    console.log('🔍 API Call: GET /admin/logs with params:', params);
    try {
      const response = await apiClient.get<ApiResponse<{ logs: SystemLog[]; pagination: any }>>('/admin/logs', { params });
      console.log('🔍 Logs Response:', response.data);

      // Transform the response to match expected structure
      return {
        data: response.data.data.logs,
        pagination: response.data.data.pagination
      };
    } catch (error) {
      console.error('🔍 Logs Error:', error);
      throw error;
    }
  },

  getLogStats: async (): Promise<LogStats> => {
    console.log('🔍 API Call: GET /admin/logs/stats');
    try {
      const response = await apiClient.get<ApiResponse<LogStats>>('/admin/logs/stats');
      console.log('🔍 Log Stats Response:', response.data);
      return response.data.data;
    } catch (error) {
      console.error('🔍 Log Stats Error:', error);
      throw error;
    }
  },

  getRecentLogs: async (limit?: number): Promise<SystemLog[]> => {
    const response = await apiClient.get<ApiResponse<SystemLog[]>>('/admin/logs/recent', {
      params: { limit },
    });
    return response.data.data;
  },

  getLog: async (id: string): Promise<SystemLog> => {
    const response = await apiClient.get<ApiResponse<SystemLog>>(`/admin/logs/${id}`);
    return response.data.data;
  },

  cleanupLogs: async (keepLastDays?: number): Promise<{ deletedCount: number }> => {
    try {
      console.log(`🔍 Cleanup API Call: DELETE /admin/logs/cleanup - keeping last ${keepLastDays} days`);
      const response = await apiClient.delete<ApiResponse<{ deletedCount: number }>>('/admin/logs/cleanup', {
        data: { keepLastDays: keepLastDays || 30 },
      });
      console.log('🔍 Cleanup Response:', response.data);
      return response.data.data;
    } catch (error) {
      console.error('🔍 Cleanup Error Details:', error);
      throw error;
    }
  },

  exportLogs: async (params?: {
    format?: 'json' | 'csv';
    level?: string;
    category?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<Blob> => {
    const response = await apiClient.get('/admin/logs/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },
};

export default AdminService;
