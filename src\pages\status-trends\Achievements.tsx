import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AchievementData } from '../StatusTrends';
import { formatDisplayNumber } from '@/utils/numberUtils';
import { 
  Award, 
  Trophy, 
  Star, 
  Target, 
  Flame, 
  Calendar, 
  TrendingUp,
  CheckCircle,
  Clock
} from 'lucide-react';

interface AchievementsProps {
  data: AchievementData[];
}

const Achievements: React.FC<AchievementsProps> = ({ data }) => {
  // Achievement type configuration
  const achievementConfig = {
    streak: {
      icon: Flame,
      color: 'text-orange-500',
      bgColor: 'bg-orange-100',
      borderColor: 'border-orange-200',
    },
    goal: {
      icon: Target,
      color: 'text-green-500',
      bgColor: 'bg-green-100',
      borderColor: 'border-green-200',
    },
    milestone: {
      icon: Trophy,
      color: 'text-blue-500',
      bgColor: 'bg-blue-100',
      borderColor: 'border-blue-200',
    },
  };

  // Separate earned and unearned achievements
  const earnedAchievements = data.filter(achievement => achievement.earned);
  const unearnedAchievements = data.filter(achievement => !achievement.earned);

  // Calculate completion percentage
  const completionPercentage = data.length > 0 
    ? (earnedAchievements.length / data.length) * 100 
    : 0;

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award className="h-5 w-5" />
          Achievements
        </CardTitle>
        
        {/* Achievement Summary */}
        <div className="mt-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Progress</span>
            <span className="text-sm text-gray-600">
              {earnedAchievements.length} / {data.length}
            </span>
          </div>
          <Progress value={completionPercentage} className="h-2" />
          <p className="text-xs text-gray-600 mt-1">
            {formatDisplayNumber(completionPercentage)}% complete
          </p>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Earned Achievements */}
        {earnedAchievements.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              Earned ({earnedAchievements.length})
            </h4>
            <div className="space-y-3">
              {earnedAchievements.map((achievement) => {
                const config = achievementConfig[achievement.type];
                const IconComponent = config.icon;
                
                return (
                  <div
                    key={achievement.id}
                    className={`p-3 rounded-lg border-2 ${config.borderColor} ${config.bgColor} relative overflow-hidden`}
                  >
                    {/* Achievement Badge */}
                    <div className="absolute top-2 right-2">
                      <Badge variant="secondary" className="text-xs">
                        <Star className="h-3 w-3 mr-1" />
                        Earned
                      </Badge>
                    </div>
                    
                    <div className="flex items-start gap-3 pr-16">
                      <div className={`p-2 rounded-lg bg-white`}>
                        <IconComponent className={`h-5 w-5 ${config.color}`} />
                      </div>
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-900">
                          {achievement.title}
                        </h5>
                        <p className="text-sm text-gray-600 mt-1">
                          {achievement.description}
                        </p>
                        {achievement.earnedDate && (
                          <div className="flex items-center gap-1 mt-2">
                            <Calendar className="h-3 w-3 text-gray-500" />
                            <span className="text-xs text-gray-500">
                              Earned {formatDate(achievement.earnedDate)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* In Progress Achievements */}
        {unearnedAchievements.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              In Progress ({unearnedAchievements.length})
            </h4>
            <div className="space-y-3">
              {unearnedAchievements.map((achievement) => {
                const config = achievementConfig[achievement.type];
                const IconComponent = config.icon;
                const progress = achievement.progress && achievement.target 
                  ? (achievement.progress / achievement.target) * 100 
                  : 0;
                
                return (
                  <div
                    key={achievement.id}
                    className="p-3 rounded-lg border border-gray-200 bg-gray-50"
                  >
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-lg bg-white border">
                        <IconComponent className="h-5 w-5 text-gray-400" />
                      </div>
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-700">
                          {achievement.title}
                        </h5>
                        <p className="text-sm text-gray-600 mt-1">
                          {achievement.description}
                        </p>
                        
                        {/* Progress Bar */}
                        {achievement.progress !== undefined && achievement.target && (
                          <div className="mt-3">
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-xs text-gray-600">Progress</span>
                              <span className="text-xs text-gray-600">
                                {achievement.progress} / {achievement.target}
                              </span>
                            </div>
                            <Progress value={progress} className="h-1.5" />
                            <p className="text-xs text-gray-500 mt-1">
                              {formatDisplayNumber(progress)}% complete
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* No Achievements */}
        {data.length === 0 && (
          <div className="text-center py-8">
            <Award className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">
              No Achievements Yet
            </h4>
            <p className="text-sm text-gray-500">
              Keep logging meals and reaching your goals to unlock achievements!
            </p>
          </div>
        )}

        {/* Motivational Message */}
        {data.length > 0 && (
          <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-orange-600" />
              <span className="font-medium text-orange-800">Keep Going!</span>
            </div>
            <p className="text-sm text-orange-700">
              {earnedAchievements.length === 0 
                ? "Start your journey by logging meals consistently to unlock your first achievement! 🌟"
                : earnedAchievements.length < 3
                ? "Great start! Keep building healthy habits to unlock more achievements! 💪"
                : earnedAchievements.length < data.length
                ? "You're doing amazing! Only a few more achievements to go! 🚀"
                : "Incredible! You've unlocked all achievements! You're a nutrition champion! 🏆"
              }
            </p>
          </div>
        )}

        {/* Achievement Categories */}
        {data.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-3">Categories</h4>
            <div className="grid grid-cols-3 gap-2">
              {Object.entries(achievementConfig).map(([type, config]) => {
                const typeAchievements = data.filter(a => a.type === type);
                const earnedCount = typeAchievements.filter(a => a.earned).length;
                const IconComponent = config.icon;
                
                return (
                  <div key={type} className="text-center p-2 rounded-lg bg-gray-50">
                    <IconComponent className={`h-5 w-5 mx-auto mb-1 ${config.color}`} />
                    <p className="text-xs font-medium capitalize">{type}</p>
                    <p className="text-xs text-gray-600">
                      {earnedCount}/{typeAchievements.length}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default Achievements;
