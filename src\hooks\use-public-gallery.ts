import { useQuery } from '@tanstack/react-query';
import GalleryService, { PublicGalleryItem } from '@/services/gallery.service';

// Hook for fetching public gallery items
export const usePublicGallery = (params?: {
  page?: number;
  limit?: number;
  category?: string;
  featured?: boolean;
  search?: string;
}) => {
  return useQuery({
    queryKey: ['public-gallery', params],
    queryFn: () => GalleryService.getGalleryItems(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook for fetching featured gallery items
export const useFeaturedGallery = (limit?: number) => {
  return useQuery({
    queryKey: ['featured-gallery', limit],
    queryFn: () => GalleryService.getFeaturedItems(limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook for fetching gallery categories
export const useGalleryCategories = () => {
  return useQuery({
    queryKey: ['gallery-categories'],
    queryFn: () => GalleryService.getCategories(),
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });
};

// Hook for fetching a single gallery item by slug
export const useGalleryItem = (slug: string) => {
  return useQuery({
    queryKey: ['gallery-item', slug],
    queryFn: () => GalleryService.getItemBySlug(slug),
    enabled: !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
