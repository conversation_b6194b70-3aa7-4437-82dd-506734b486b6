
import React, { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Button } from "@/components/ui/button";
import { Camera, Upload, X } from "lucide-react";
import { toast } from "sonner";

interface UploadZoneProps {
  onImageSelected: (imageFile: File, previewUrl: string) => void;
}

const UploadZone: React.FC<UploadZoneProps> = ({ onImageSelected }) => {
  const [preview, setPreview] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      const file = acceptedFiles[0];

      if (!file.type.startsWith("image/")) {
        toast.error("Please upload an image file");
        return;
      }

      setUploading(true);

      // Create a preview
      const previewUrl = URL.createObjectURL(file);
      setPreview(previewUrl);

      // Simulate upload delay
      setTimeout(() => {
        onImageSelected(file, previewUrl);
        setUploading(false);
      }, 1000);
    },
    [onImageSelected]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [],
    },
    maxFiles: 1,
  });

  const removeImage = () => {
    setPreview(null);
  };

  return (
    <div className="w-full">
      {!preview ? (
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive
              ? "border-nutrisnap-teal bg-nutrisnap-green"
              : "border-gray-300 hover:border-nutrisnap-teal"
          }`}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="p-4 bg-nutrisnap-teal/10 rounded-full">
              <Camera className="h-10 w-10 text-nutrisnap-teal" />
            </div>
            <div>
              <p className="text-lg font-medium">
                {isDragActive
                  ? "Drop your food image here..."
                  : "Upload your food image"}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Drag & drop or click to browse
              </p>
            </div>
            <Button
              type="button"
              className="bg-nutrisnap-teal hover:bg-nutrisnap-teal/90"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Image
            </Button>
          </div>
        </div>
      ) : (
        <div className="relative">
          <div className="rounded-lg overflow-hidden border border-gray-200">
            <img
              src={preview}
              alt="Food preview"
              className="w-full h-64 object-cover"
            />
          </div>
          <div className="absolute top-2 right-2">
            <Button
              variant="destructive"
              size="icon"
              onClick={removeImage}
              className="h-8 w-8 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          {uploading && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
              <div className="p-4 bg-white rounded-lg flex flex-col items-center">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-nutrisnap-teal"></div>
                <p className="mt-2 text-sm font-medium">Processing image...</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default UploadZone;
