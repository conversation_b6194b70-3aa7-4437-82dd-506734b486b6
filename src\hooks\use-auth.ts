import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import AuthService, { 
  LoginCredentials, 
  RegisterData, 
  User 
} from '@/services/auth.service';
import { toast } from 'sonner';

export const useLogin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (credentials: LoginCredentials) => AuthService.login(credentials),
    onSuccess: (data) => {
      // Save token and user data to localStorage
      localStorage.setItem('nutrisnap_token', data.token);
      localStorage.setItem('nutrisnap_user', JSON.stringify(data.user));
      
      // Invalidate user profile query to refetch
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      
      toast.success(`Welcome back, ${data.user.firstName}!`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Login failed');
    }
  });
};

export const useRegister = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (userData: RegisterData) => AuthService.register(userData),
    onSuccess: (data) => {
      // Save token and user data to localStorage
      localStorage.setItem('nutrisnap_token', data.token);
      localStorage.setItem('nutrisnap_user', JSON.stringify(data.user));
      
      // Invalidate user profile query to refetch
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      
      toast.success(`Welcome, ${data.user.firstName}!`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Registration failed');
    }
  });
};

export const useUserProfile = () => {
  return useQuery({
    queryKey: ['userProfile'],
    queryFn: () => AuthService.getProfile(),
    // Only fetch if we have a token
    enabled: !!localStorage.getItem('nutrisnap_token'),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    onError: () => {
      // If there's an error fetching the profile, clear local storage
      localStorage.removeItem('nutrisnap_token');
      localStorage.removeItem('nutrisnap_user');
    }
  });
};

export const useUpdatePreferences = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (preferences: {
      dietaryRestrictions?: string[];
      nutritionGoals?: {
        calories?: number;
        protein?: number;
        carbs?: number;
        fat?: number;
      };
      units?: 'metric' | 'imperial';
    }) => AuthService.updatePreferences(preferences),
    onSuccess: (data) => {
      // Update user in localStorage
      localStorage.setItem('nutrisnap_user', JSON.stringify(data.user));
      
      // Invalidate user profile query to refetch
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      
      toast.success('Preferences updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update preferences');
    }
  });
};

export const useLogout = () => {
  const queryClient = useQueryClient();
  
  return () => {
    // Clear local storage
    localStorage.removeItem('nutrisnap_token');
    localStorage.removeItem('nutrisnap_user');
    
    // Reset all queries
    queryClient.clear();
    
    toast.info('You have been logged out');
  };
};
