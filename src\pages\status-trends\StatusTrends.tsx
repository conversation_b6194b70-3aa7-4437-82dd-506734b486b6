
import React from "react";
import { mockFoodAnalyses } from "@/data/mockData";
import { generateCalendarData, generateTrendData, calculateSummary, type FoodAnalysis } from "./utils/trendUtils";
import AnalysisCalendar from "./components/AnalysisCalendar";
import SummaryStats from "./components/SummaryStats";
import CalorieTrend from "./components/CalorieTrend";
import MacroTrend from "./components/MacroTrend";

const StatusTrends = () => {
  // Generate data using utility functions
  const calendarData = generateCalendarData(mockFoodAnalyses as FoodAnalysis[]);
  const trendData = generateTrendData(mockFoodAnalyses as FoodAnalysis[]);
  const summary = calculateSummary(trendData);
  
  return (
    <div className="flex min-h-screen bg-gradient-to-br from-white to-gray-50">
      <div className="flex-1 p-6 md:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8 animate-fade-in">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-nutrisnap-charcoal to-nutrisnap-teal bg-clip-text text-transparent">
              Status & Trends
            </h1>
            <p className="text-gray-500 mt-1">
              Track your progress and view historical nutrition data
            </p>
          </div>

          {/* Analysis Calendar */}
          <div className="animate-fade-in" style={{ animationDelay: "0.1s" }}>
            <AnalysisCalendar calendarData={calendarData} />
          </div>

          {/* Summary Statistics */}
          <div className="animate-fade-in" style={{ animationDelay: "0.2s" }}>
            <SummaryStats 
              summary={summary} 
              totalAnalyses={mockFoodAnalyses.length} 
            />
          </div>

          {/* Trend Graphs */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-fade-in" style={{ animationDelay: "0.3s" }}>
            <CalorieTrend trendData={trendData} />
            <MacroTrend trendData={trendData} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatusTrends;
