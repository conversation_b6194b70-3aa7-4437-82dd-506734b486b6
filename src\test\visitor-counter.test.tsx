import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import VisitorCounter from '@/components/VisitorCounter';
import VisitorService from '@/services/visitor.service';

// Mock the visitor service
vi.mock('@/services/visitor.service');

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('VisitorCounter', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state initially', () => {
    vi.mocked(VisitorService.getVisitorStats).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    renderWithQueryClient(<VisitorCounter />);
    
    expect(screen.getByText('Live Visitor Stats')).toBeInTheDocument();
    // Should show loading skeleton
    expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('renders visitor stats when data is loaded', async () => {
    const mockStats = {
      onlineNow: 5,
      todayVisitors: 123,
      weeklyVisitors: 456,
      totalVisitors: 1234,
      lastUpdated: new Date().toISOString()
    };

    vi.mocked(VisitorService.getVisitorStats).mockResolvedValue(mockStats);

    renderWithQueryClient(<VisitorCounter />);

    await waitFor(() => {
      expect(screen.getByText('Live Visitor Stats')).toBeInTheDocument();
      expect(screen.getByText('Online Now')).toBeInTheDocument();
      expect(screen.getByText("Today's Visitors")).toBeInTheDocument();
      expect(screen.getByText('This Week')).toBeInTheDocument();
      expect(screen.getByText('Total Visitors')).toBeInTheDocument();
    });
  });

  it('renders compact version correctly', async () => {
    const mockStats = {
      onlineNow: 5,
      todayVisitors: 123,
      weeklyVisitors: 456,
      totalVisitors: 1234,
      lastUpdated: new Date().toISOString()
    };

    vi.mocked(VisitorService.getVisitorStats).mockResolvedValue(mockStats);

    renderWithQueryClient(<VisitorCounter compact showTitle={false} />);

    await waitFor(() => {
      expect(screen.getByText('1,234 visitors')).toBeInTheDocument();
      expect(screen.getByText('5 online')).toBeInTheDocument();
    });

    // Should not show the full title in compact mode
    expect(screen.queryByText('Live Visitor Stats')).not.toBeInTheDocument();
  });

  it('handles API errors gracefully', async () => {
    vi.mocked(VisitorService.getVisitorStats).mockRejectedValue(
      new Error('API Error')
    );

    renderWithQueryClient(<VisitorCounter />);

    // Component should not render anything on error
    await waitFor(() => {
      expect(screen.queryByText('Live Visitor Stats')).not.toBeInTheDocument();
    });
  });
});
