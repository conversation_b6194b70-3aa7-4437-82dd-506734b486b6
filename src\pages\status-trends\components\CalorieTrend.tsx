
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";

interface CalorieTrendProps {
  trendData: Array<{
    date: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    analysisCount: number;
  }>;
}

const CalorieTrend: React.FC<CalorieTrendProps> = ({ trendData }) => {
  return (
    <Card className="shadow-md overflow-hidden border-none bg-white hover:shadow-lg transition-shadow duration-300">
      <CardHeader className="bg-gradient-to-r from-nutrisnap-teal/10 to-transparent pb-4">
        <CardTitle className="text-lg font-medium text-nutrisnap-charcoal flex items-center gap-2">
          <span className="inline-block w-3 h-3 bg-nutrisnap-teal rounded-full"></span>
          Calorie Intake Over Time
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="h-64 relative">
          {/* Simple trend line visualization */}
          <div className="absolute inset-0 flex items-end">
            {trendData.slice(-14).map((day, i) => (
              <div 
                key={i} 
                className="flex-1 flex flex-col items-center justify-end h-full"
              >
                <div 
                  className="w-4 bg-gradient-to-t from-nutrisnap-teal to-nutrisnap-teal/80 rounded-t-md transform hover:scale-110 transition-transform duration-200" 
                  style={{ 
                    height: `${Math.min(day.calories / 20, 100)}%`,
                    opacity: day.analysisCount === 0 ? 0.2 : 1
                  }}
                ></div>
                <div className="text-xs mt-1 text-gray-500">
                  {new Date(day.date).getDate()}
                </div>
              </div>
            ))}
          </div>
          {/* Goal line */}
          <div 
            className="absolute border-t border-dashed border-red-400 w-full" 
            style={{ bottom: `${2000 / 20}%` }}
          >
            <span className="absolute right-0 -top-4 text-xs text-red-500 font-medium bg-white/70 rounded px-1">
              2000 kcal goal
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CalorieTrend;
