/**
 * Utility functions for handling image URLs
 */

/**
 * Converts a relative image URL to a full URL using the backend base URL
 * @param imageUrl - The image URL (can be relative or absolute)
 * @returns Full image URL
 */
export const getImageUrl = (imageUrl: string): string => {
  // Cloudinary URLs are always full URLs starting with https://res.cloudinary.com
  if (imageUrl.startsWith('https://res.cloudinary.com') || imageUrl.startsWith('http')) {
    return imageUrl;
  }

  // For backward compatibility with local uploads (relative paths)
  const backendUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:5000';
  return `${backendUrl}${imageUrl}`;
};

/**
 * Checks if an image URL is valid
 * @param imageUrl - The image URL to validate
 * @returns True if the URL appears to be valid
 */
export const isValidImageUrl = (imageUrl: string): boolean => {
  if (!imageUrl || typeof imageUrl !== 'string') {
    return false;
  }
  
  // Check if it's a valid URL format
  try {
    const url = imageUrl.startsWith('http') ? imageUrl : getImageUrl(imageUrl);
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Gets a placeholder image URL for broken or missing images
 * @returns Placeholder image URL
 */
export const getPlaceholderImageUrl = (): string => {
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTJhNSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pgo8L3N2Zz4K';
};
