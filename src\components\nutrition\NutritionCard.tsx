
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/contexts/AuthContext";
import { ArrowUp, ArrowDown } from "lucide-react";

type NutritionData = {
  label: string;
  value: number;
  unit: string;
  goal: number;
  color: string;
  icon?: React.ReactNode;
};

type NutritionCardProps = {
  data: NutritionData;
  showProgress?: boolean;
  comparison?: number;
};

const NutritionCard = ({
  data,
  showProgress = true,
  comparison,
}: NutritionCardProps) => {
  const { user } = useAuth();
  const percentage = Math.min(Math.round((data.value / data.goal) * 100), 100);

  const getProgressColor = (percentage: number) => {
    if (percentage < 25) return "bg-blue-500";
    if (percentage < 50) return "bg-green-500";
    if (percentage < 75) return "bg-yellow-500";
    if (percentage < 90) return "bg-orange-500";
    return "bg-red-500";
  };

  return (
    <Card className="shadow-sm border overflow-hidden">
      <CardHeader className="p-4 pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-sm font-medium text-gray-500">
          {data.label}
        </CardTitle>
        {data.icon}
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <div className="flex justify-between items-end">
          <div>
            <p className="text-2xl font-bold">{data.value}</p>
            <p className="text-xs text-gray-500">{data.unit}</p>
          </div>
          {comparison !== undefined && (
            <div className="flex items-center">
              {comparison > 0 ? (
                <div className="flex items-center text-green-500 text-sm">
                  <ArrowUp className="h-3 w-3 mr-1" />
                  {comparison}%
                </div>
              ) : comparison < 0 ? (
                <div className="flex items-center text-red-500 text-sm">
                  <ArrowDown className="h-3 w-3 mr-1" />
                  {Math.abs(comparison)}%
                </div>
              ) : (
                <div className="text-gray-500 text-sm">0%</div>
              )}
            </div>
          )}
        </div>
        
        {showProgress && (
          <div className="mt-2">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Progress</span>
              <span>{percentage}%</span>
            </div>
            <Progress 
              value={percentage} 
              className={`h-2 ${getProgressColor(percentage)}`} 
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default NutritionCard;
