import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis } from 'recharts';
import { MealPatternData } from '../StatusTrends';
import { formatDisplayNumber, formatCalories } from '@/utils/numberUtils';
import { Clock, Coffee, Sun, Moon, Utensils } from 'lucide-react';

interface MealPatternsProps {
  data: MealPatternData[];
}

const MealPatterns: React.FC<MealPatternsProps> = ({ data }) => {
  // Meal category configuration
  const mealConfig = {
    breakfast: {
      icon: Coffee,
      color: '#fbbf24',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-800',
    },
    lunch: {
      icon: Sun,
      color: '#34d399',
      bgColor: 'bg-green-100',
      textColor: 'text-green-800',
    },
    dinner: {
      icon: Moon,
      color: '#60a5fa',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-800',
    },
    snack: {
      icon: Utensils,
      color: '#a78bfa',
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-800',
    },
  };

  // Prepare data for charts
  const pieData = data.map(item => ({
    ...item,
    fill: mealConfig[item.category as keyof typeof mealConfig]?.color || '#6b7280',
  }));

  const barData = data.map(item => ({
    ...item,
    categoryLabel: item.category.charAt(0).toUpperCase() + item.category.slice(1),
  }));

  // Calculate totals
  const totalMeals = data.reduce((sum, item) => sum + item.count, 0);
  const avgCaloriesPerMeal = data.reduce((sum, item) => sum + item.avgCalories, 0) / data.length;

  // Custom tooltip for pie chart
  const PieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium capitalize">{data.category}</p>
          <p className="text-sm text-gray-600">
            {data.count} meals ({formatDisplayNumber(data.percentage)}%)
          </p>
          <p className="text-sm text-gray-600">
            Avg: {formatCalories(data.avgCalories)} cal
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom tooltip for bar chart
  const BarTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-gray-600">
            Average: {formatCalories(data.avgCalories)} calories
          </p>
          <p className="text-sm text-gray-600">
            {data.count} meals logged
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Meal Patterns
        </CardTitle>
        
        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">Total Meals</p>
            <p className="text-lg font-semibold">{totalMeals}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Avg per Meal</p>
            <p className="text-lg font-semibold">
              {formatCalories(avgCaloriesPerMeal)} cal
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Meal Distribution Pie Chart */}
        <div>
          <h4 className="text-sm font-medium mb-3">Meal Distribution</h4>
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={2}
                  dataKey="count"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <Tooltip content={<PieTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Meal Category Breakdown */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Category Breakdown</h4>
          {data.map((item) => {
            const config = mealConfig[item.category as keyof typeof mealConfig];
            const IconComponent = config?.icon || Utensils;
            
            return (
              <div key={item.category} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${config?.bgColor || 'bg-gray-100'}`}>
                    <IconComponent className={`h-4 w-4 ${config?.textColor || 'text-gray-600'}`} />
                  </div>
                  <div>
                    <p className="font-medium capitalize">{item.category}</p>
                    <p className="text-xs text-gray-600">
                      {item.count} meals • {formatDisplayNumber(item.percentage)}%
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold">
                    {formatCalories(item.avgCalories)} cal
                  </p>
                  <p className="text-xs text-gray-600">avg per meal</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Average Calories by Meal Type */}
        <div>
          <h4 className="text-sm font-medium mb-3">Average Calories by Meal</h4>
          <div className="h-32">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={barData}>
                <XAxis 
                  dataKey="categoryLabel" 
                  fontSize={10}
                  tick={{ fontSize: 10 }}
                />
                <YAxis 
                  fontSize={10}
                  tick={{ fontSize: 10 }}
                  tickFormatter={(value) => formatCalories(value)}
                />
                <Tooltip content={<BarTooltip />} />
                <Bar 
                  dataKey="avgCalories" 
                  fill="#3b82f6"
                  radius={[2, 2, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Insights */}
        <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-800">Pattern Insights</span>
          </div>
          <div className="space-y-1 text-sm text-blue-700">
            {/* Most frequent meal */}
            {data.length > 0 && (
              <p>
                • Most frequent: {data.reduce((prev, current) => 
                  prev.count > current.count ? prev : current
                ).category} ({data.reduce((prev, current) => 
                  prev.count > current.count ? prev : current
                ).count} meals)
              </p>
            )}
            
            {/* Highest calorie meal */}
            {data.length > 0 && (
              <p>
                • Highest calories: {data.reduce((prev, current) => 
                  prev.avgCalories > current.avgCalories ? prev : current
                ).category} (avg {formatCalories(data.reduce((prev, current) => 
                  prev.avgCalories > current.avgCalories ? prev : current
                ).avgCalories)} cal)
              </p>
            )}
            
            {/* Recommendations */}
            {totalMeals < 21 && (
              <p>• Try to log meals more consistently for better insights</p>
            )}
            
            {data.find(item => item.category === 'snack')?.percentage > 40 && (
              <p>• Consider balancing snacks with regular meals</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MealPatterns;
