
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { SettingsSectionProps } from "../types";

const NotificationsSection: React.FC<SettingsSectionProps> = ({ formData, setFormData }) => {
  const handleNotificationChange = (
    field: keyof typeof formData.notifications,
    value: boolean
  ) => {
    setFormData({
      ...formData,
      notifications: {
        ...formData.notifications,
        [field]: value,
      },
    });
  };

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle>Notifications</CardTitle>
        <CardDescription>Manage your notification preferences</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Meal Reminders</p>
              <p className="text-sm text-gray-500">
                Get reminders to log your meals
              </p>
            </div>
            <Switch
              checked={formData.notifications.mealReminders}
              onCheckedChange={(value) =>
                handleNotificationChange("mealReminders", value)
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Weekly Reports</p>
              <p className="text-sm text-gray-500">
                Receive weekly nutrition summaries
              </p>
            </div>
            <Switch
              checked={formData.notifications.weeklyReports}
              onCheckedChange={(value) =>
                handleNotificationChange("weeklyReports", value)
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Achievement Alerts</p>
              <p className="text-sm text-gray-500">
                Get notified when you reach nutrition goals
              </p>
            </div>
            <Switch
              checked={formData.notifications.achievementAlerts}
              onCheckedChange={(value) =>
                handleNotificationChange("achievementAlerts", value)
              }
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationsSection;
