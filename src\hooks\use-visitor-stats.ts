import { useQuery, useMutation } from '@tanstack/react-query';
import VisitorService, { VisitorStats } from '@/services/visitor.service';
import { useEffect } from 'react';

// Hook for fetching visitor statistics
export const useVisitorStats = (autoRefresh = true) => {
  const query = useQuery({
    queryKey: ['visitor-stats'],
    queryFn: VisitorService.getVisitorStats,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: autoRefresh ? 30 * 1000 : false, // Auto-refresh every 30 seconds
    refetchIntervalInBackground: false,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  return query;
};

// Hook for tracking visitor
export const useTrackVisitor = () => {
  const mutation = useMutation({
    mutationFn: VisitorService.trackVisitor,
    retry: false, // Don't retry visitor tracking
  });

  // Auto-track visitor on mount
  useEffect(() => {
    mutation.mutate();
  }, []);

  return mutation;
};

// Combined hook that tracks visitor and fetches stats
export const useVisitorTracking = (autoRefresh = true) => {
  const stats = useVisitorStats(autoRefresh);
  const tracking = useTrackVisitor();

  return {
    stats: stats.data,
    isLoading: stats.isLoading,
    error: stats.error,
    isTracking: tracking.isPending,
    refetch: stats.refetch,
  };
};
