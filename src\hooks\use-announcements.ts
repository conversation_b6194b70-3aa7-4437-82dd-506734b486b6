import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import AnnouncementsService from '@/services/announcements.service';
import { toast } from 'sonner';

export const useActiveAnnouncements = () => {
  return useQuery({
    queryKey: ['activeAnnouncements'],
    queryFn: () => AnnouncementsService.getActiveAnnouncements(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Admin only queries and mutations
export const useAllAnnouncements = (params?: {
  page?: number;
  limit?: number;
  active?: boolean;
  type?: string;
  priority?: string;
}) => {
  return useQuery({
    queryKey: ['allAnnouncements', params],
    queryFn: () => AnnouncementsService.getAllAnnouncements(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useAnnouncement = (id: string) => {
  return useQuery({
    queryKey: ['announcement', id],
    queryFn: () => AnnouncementsService.getAnnouncementById(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id,
  });
};

export const useCreateAnnouncement = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => AnnouncementsService.createAnnouncement(data),
    onSuccess: () => {
      // Invalidate announcements queries to refetch
      queryClient.invalidateQueries({ queryKey: ['activeAnnouncements'] });
      queryClient.invalidateQueries({ queryKey: ['allAnnouncements'] });
      
      toast.success('Announcement created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create announcement');
    }
  });
};

export const useUpdateAnnouncement = (id: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => AnnouncementsService.updateAnnouncement(id, data),
    onSuccess: () => {
      // Invalidate specific announcement query to refetch
      queryClient.invalidateQueries({ queryKey: ['announcement', id] });
      // Invalidate announcements lists
      queryClient.invalidateQueries({ queryKey: ['activeAnnouncements'] });
      queryClient.invalidateQueries({ queryKey: ['allAnnouncements'] });
      
      toast.success('Announcement updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update announcement');
    }
  });
};

export const useDeleteAnnouncement = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => AnnouncementsService.deleteAnnouncement(id),
    onSuccess: () => {
      // Invalidate announcements queries to refetch
      queryClient.invalidateQueries({ queryKey: ['activeAnnouncements'] });
      queryClient.invalidateQueries({ queryKey: ['allAnnouncements'] });
      
      toast.success('Announcement deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete announcement');
    }
  });
};
